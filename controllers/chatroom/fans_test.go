package chatroom

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansbox"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxrewardlog"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxtask"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxusertask"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestFansTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(fansRankResp{}, "data", "my_medal", "rule", "fans_count", "medal_limit", "gift", "medal_gift", "tasks",
		"normal_privilege", "super_privilege", "fans_box")
	kc.CheckOmitEmpty(fansRankResp{}, "my_medal", "gift", "medal_gift", "tasks", "normal_privilege", "super_privilege", "fans_box")
	kc.Check(fansRankElem{}, "rank", "titles")
	kc.Check(myMedal{}, "rank_up", "rank", "medal_full", "max_level")
	kc.Check(obtainMedalGift{}, "gift_id", "name", "price", "icon_url")
	kc.Check(fansProgressResp{}, "rule", "revenue", "threshold", "medal", "contact")
	kc.Check(fansProgressResp{}.Contact, "qq_group")
	kc.Check(fansBox{}, "level", "name", "icon_url", "reward_tip", "join_energy", "next_level", "box_task", "user_task")
	kc.CheckOmitEmpty(fansBox{}, "next_level", "user_task")
	kc.Check(nextLevel{}, "level", "name", "fans_num")
	kc.Check(boxTask{}, "id", "target_energy", "current_energy", "status")
	kc.Check(userTask{}, "status", "prize_tip", "prizes")
	kc.CheckOmitEmpty(userTask{}, "prize_tip", "prizes")
	kc.Check(prizeInfo{}, "prize_type", "prize_element_id", "prize_name", "prize_icon_url", "prize_num")
}

func TestNewFansRankParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	c := handler.CreateTestContext(false)
	c.C.Request, _ = http.NewRequest("GET", "/fans/rank?room_id=-123&type=1", nil)
	_, err := newFansRankParam(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("GET", "/fans/rank?room_id=22489473&type=1", nil)
	param, err := newFansRankParam(c)
	require.NoError(err)
	assert.True(param.isTop3)
	assert.NotNil(param.user)
	assert.NotNil(param.Data)
}

func TestFansRankFindRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := fansRankParam{isTop3: true, roomID: 22489473}
	assert.NoError(param.findRank())
	require.NotEmpty(param.Data)
	for i := range param.Data {
		assert.Equal(int64(i+1), param.Data[i].Rank)
	}

	param = fansRankParam{isTop3: true, roomID: 1234567}
	assert.NoError(param.findRank())
	found := false
	for _, title := range param.Data[0].Titles {
		if title.Type == liveuser.TitleTypeMedal {
			found = true
			break
		}
	}
	assert.True(found)
}

func TestFansRankFindMyMedal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := livemedal.Collection()
	s := &livemedal.Simple{RoomID: 22489473, CreatorID: 10, UserID: 12, Status: livemedal.StatusOwned, Point: 200}
	err := collection.FindOneAndUpdate(ctx, bson.M{"room_id": 22489473, "user_id": 12},
		bson.M{"$set": s}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	require.False(err != nil && !mongodb.IsNoDocumentsError(err), err)

	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	param := fansRankParam{c: c}
	param.Data = make([]*fansRankElem, 3)
	for i := 0; i < len(param.Data); i++ {
		param.Data[i] = &fansRankElem{Simple: &livemedal.Simple{Point: 1000}}
	}
	param.user = new(user.User)
	param.user.ID = 12
	param.roomID = 22489473
	param.creatorID = 10
	param.findMyMedal()
	require.NotNil(param.MyMedal.LiveMedal)
	assert.Equal(livemedal.UserMedalLevelLimit(), param.MyMedal.MaxLevel)
	assert.GreaterOrEqual(int64(801), param.MyMedal.RankUp)
	param.Data[1].UserID = 12
	param.Data[0].Point = 300
	param.findMyMedal()
	assert.Equal(int64(101), param.MyMedal.RankUp)
	assert.Equal(2, param.MyMedal.Rank)
	param.user.ID = 100
	param.findMyMedal()
	assert.Equal(int64(60), param.MyMedal.RankUp)
	param.user.ID = 10
	param.MyMedal = nil
	param.findMyMedal()
	assert.Nil(param.MyMedal)

	collection = livemedal.Collection()
	s = &livemedal.Simple{RoomID: 1234567, CreatorID: 10, UserID: 3456835, Status: livemedal.StatusShow, Point: 200}
	err = collection.FindOneAndUpdate(ctx, bson.M{"room_id": 1234567, "user_id": 3456835},
		bson.M{"$set": s}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	require.False(err != nil && err != mongo.ErrNoDocuments, err)
	param.user.ID = 3456835
	param.roomID = 1234567
	param.findMyMedal()
	assert.Equal("https://static-test.missevan.com/live/medalframes/3f12/level02_0_9_0_54.png", param.MyMedal.FrameURL)
}

func TestFansRankTrimRedundancy(t *testing.T) {
	assert := assert.New(t)
	param := fansRankParam{isTop3: true}
	param.trimRedundancy()
	param.Data = make([]*fansRankElem, 5)
	param.trimRedundancy()
	assert.Len(param.Data, 3)
}

func TestFansRankCheckRankInvisible(t *testing.T) {
	assert := assert.New(t)

	param := fansRankParam{user: new(user.User)}
	param.user.ID = 10
	assert.NotPanics(func() { param.checkRankInvisible() })
	param.Data = []*fansRankElem{{
		Simple: &livemedal.Simple{
			RankInvisible: true,
			CreatorID:     123,
			UserID:        10,
		},
	}, {
		Simple: &livemedal.Simple{
			RankInvisible: true,
			CreatorID:     123,
			UserID:        11,
		},
	},
	}
	param.checkRankInvisible()
	d := param.Data
	assert.Equal([]string{"", "神秘人"}, []string{d[0].Username, d[1].Username})
}

func TestActionFansRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/fans/rank?room_id=22489473&type=1", true, nil)
	r, err := ActionFansRank(c)
	require.NoError(err)
	resp := r.(fansRankResp)
	assert.NotNil(resp.MyMedal)
	assert.NotEmpty(resp.Data)
	tutil.PrintJSON(r)
}

func TestFansRankParam_BuildObtainMedalGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	creatorID := int64(20230217)
	lm := livemedal.LiveMedal{
		Simple: livemedal.Simple{
			CreatorID: creatorID,
			UserID:    creatorID,
			Status:    livemedal.StatusOwned,
		},
	}
	_, err := livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": creatorID})
	require.NoError(err)
	_, err = livemedal.Collection().InsertOne(ctx, lm)
	require.NoError(err)
	c := handler.NewTestContext(http.MethodPost, "/", true, nil)
	p := fansRankParam{
		c:         c,
		creatorID: creatorID,
		user: &user.User{
			IUser: user.IUser{
				ID: creatorID,
			},
		},
	}
	key := keys.KeyRoomMedalPointMulti.Format(p.creatorID)
	require.NoError(service.DB.Table(liverecommendedelements.TableName()).Delete("", "element_type = ?",
		liverecommendedelements.ElementMultiMedalPoint).Error)
	require.NoError(service.LRURedis.Del(key).Err())
	p.buildObtainMedalGift()
	require.NotNil(p.Gift)
	assert.Equal(livemedal.ObtainMedalGiftID, p.Gift.GiftID)

	now := goutil.TimeNow()
	err = liverecommendedelements.BatchAddRoomMedalPointMulti([]int64{p.creatorID}, now, now.Add(5*time.Second), liverecommendedelements.PointMulti{
		PointMultiAdd: 1,
	})
	require.NoError(err)
	require.NoError(service.LRURedis.Del(key).Err())
	p.buildObtainMedalGift()
	require.NotNil(p.Gift)
	assert.Equal(livemedal.ObtainMedalHalfPriceGiftID, p.Gift.GiftID)

	_, err = livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": p.c.UserID()})
	require.NoError(err)
	p.buildObtainMedalGift()
	require.NotNil(p.Gift)
	assert.Equal(livemedal.ObtainMedalDiscountGiftID, p.Gift.GiftID)
}

func TestActionFansTasks(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := fansRankParam{
		roomID: 20230222,
		user: &user.User{IUser: user.IUser{
			ID: 2,
		}},
		isTop3: true,
		fansRankResp: fansRankResp{
			MyMedal: &myMedal{
				LiveMedal: &livemedal.LiveMedal{
					Simple: livemedal.Simple{Status: livemedal.StatusOwned},
				},
			},
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := liveuser.LiveUsersCollection().DeleteOne(ctx, bson.M{"room_id": param.roomID, "user_id": param.user.ID})
	require.NoError(err)
	_, err = livegifts.Collection().DeleteMany(ctx, bson.M{"room_id": param.roomID, "user_id": param.user.ID,
		"gift_id": useritems.GiftIDCatFood})
	require.NoError(err)

	param.buildMedalTasks()
	require.Len(param.Tasks, 6)
	for i := range param.Tasks {
		assert.Zero(param.Tasks[i].Status, param.Tasks[i].Name)
	}

	now := goutil.TimeNow()
	_, err = liveuser.LiveUsersCollection().InsertOne(ctx,
		liveuser.LiveUser{
			UserID:          param.user.ID,
			RoomID:          param.roomID,
			TodayTimeOnline: now,
			TodayAcqOnline:  30 * 1000 * util.SecondOneMinute,
			StatusShare:     1,
			TimeShare:       now,
		})
	require.NoError(err)
	param.buildMedalTasks()
	require.Len(param.Tasks, 6)
	assert.NotZero(param.Tasks[1].Status, param.Tasks[1].Name)
	assert.NotZero(param.Tasks[2].Status, param.Tasks[2].Name)
	assert.NotZero(param.Tasks[3].Status, param.Tasks[3].Name)
	assert.Zero(param.Tasks[4].Status, param.Tasks[4].Name)
	assert.Zero(param.Tasks[5].Status, param.Tasks[5].Name)

	param.MyMedal.SuperFan = &livemedal.SuperFan{
		ExpireTime: now.Unix() + 5,
	}
	param.buildMedalTasks()
	require.NoError(err)
	require.Len(param.Tasks, 6)
	assert.NotZero(param.Tasks[5].Status, param.Tasks[5].Name)

	_, err = livegifts.Collection().InsertOne(ctx,
		livegifts.LiveGift{
			UserID:   param.user.ID,
			RoomID:   param.roomID,
			GiftID:   useritems.GiftIDCatFood,
			SentTime: now,
		})
	require.NoError(err)
	param.MyMedal.CreatedTime = now.Add(-10 * time.Second)
	param.buildMedalTasks()
	require.NoError(err)
	require.Len(param.Tasks, 6)
	assert.NotZero(param.Tasks[0].Status, param.Tasks[0].Name)

	param.MyMedal.CreatedTime = now.Add(10 * time.Second)
	param.buildMedalTasks()
	require.NoError(err)
	require.Len(param.Tasks, 6)
	assert.Zero(param.Tasks[0].Status, param.Tasks[0].Name)
}

func TestActionFansProgress(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/fans/progress?room_id=22489473", false, nil)
	c.Equip().OS = goutil.IOS
	c.Equip().AppVersion = "4.3.6"
	r, err := ActionFansProgress(c)
	require.NoError(err)
	resp := r.(*fansProgressResp)
	assert.NotNil(resp.Medal)
	assert.Equal(int64(30000), resp.Threshold)
	assert.LessOrEqual(resp.Revenue, resp.Threshold)
	assert.Equal(liveQQGroup, resp.Contact.QQGroup)

	c.C.Request, _ = http.NewRequest("GET", "/fans/progress?creator_id=10", nil)
	r, err = ActionFansProgress(c)
	require.NoError(err)
	resp = r.(*fansProgressResp)
	assert.NotNil(resp.Medal)
	assert.Equal(int64(30000), resp.Threshold)
	assert.LessOrEqual(resp.Revenue, resp.Threshold)
	assert.Equal(liveQQGroup, resp.Contact.QQGroup)
}

func TestFansRankParam_buildLiveFansBox(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试获取粉丝团宝箱信息
	before := config.Conf.Params.MedalParams.EnableFansBox
	defer func() {
		config.Conf.Params.MedalParams.EnableFansBox = before
	}()

	config.Conf.Params.MedalParams.EnableFansBox = true
	now := goutil.TimeNow()
	require.NoError(livefansboxtask.LiveFansBoxTask{}.DB().Delete("", "room_id = ?", testRoomID).Error)
	testTask := &livefansboxtask.LiveFansBoxTask{
		Level:     livefansbox.Level2,
		Bizdate:   now.Format(goutil.TimeFormatYMD),
		RoomID:    testRoomID,
		FansCount: 50,
		Energy:    2333,
		Status:    livefansboxtask.StatusFinished,
	}
	require.NoError(livefansboxtask.LiveFansBoxTask{}.DB().Create(testTask).Error)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": 12, "room_id": testRoomID})
	require.NoError(err)
	_, err = livemedal.Collection().InsertOne(ctx, &livemedal.LiveMedal{
		Simple: livemedal.Simple{
			RoomID: testRoomID,
			UserID: 12,
			Status: livemedal.StatusOwned,
		},
	})
	require.NoError(err)

	require.NoError(livefansboxusertask.LiveFansBoxUserTask{}.DB().Delete("", "room_id = ?", testRoomID).Error)
	testUserTask := &livefansboxusertask.LiveFansBoxUserTask{
		RoomID:        testRoomID,
		UserID:        12,
		FansBoxTaskID: testTask.ID,
		UserEnergy:    2233,
	}
	require.NoError(testUserTask.DB().Create(testUserTask).Error)

	require.NoError(livefansboxrewardlog.LiveFansBoxRewardLog{}.DB().Delete("", "room_id = ?", testRoomID).Error)
	log := &livefansboxrewardlog.LiveFansBoxRewardLog{
		RoomID:            testRoomID,
		UserID:            12,
		Level:             livefansbox.Level2,
		FansBoxTaskID:     testTask.ID,
		FansBoxUserTaskID: testUserTask.ID,
		More:              []byte("{\"reward\":{\"type\":3,\"prize_ids\":[13]}}"),
	}
	require.NoError(log.DB().Create(log).Error)

	param := &fansRankParam{
		user: &user.User{
			IUser: user.IUser{
				ID: 12,
			},
		},
		roomID:    testRoomID,
		creatorID: 2333,
	}
	param.buildLiveFansBox()
	require.NotNil(param.fansRankResp.FansBox)
	assert.Equal(livefansbox.Level2, param.fansRankResp.FansBox.Level)
	assert.Equal("粉丝团宝箱 2", param.fansRankResp.FansBox.Name)
	assert.Equal("https://static-test.missevan.com/test/box2.png", param.fansRankResp.FansBox.IconURL)
	assert.Equal("可能开出：fans_box_avatar_frame", param.fansRankResp.FansBox.RewardTip)
	assert.Equal(livefansboxtask.JoinEnergy, param.fansRankResp.FansBox.JoinEnergy)
	require.Nil(param.fansRankResp.FansBox.NextLevel)
	assert.Equal(testTask.ID, param.fansRankResp.FansBox.BoxTask.ID)
	assert.Equal(1200, param.fansRankResp.FansBox.BoxTask.TargetEnergy)
	assert.Equal(1200, param.fansRankResp.FansBox.BoxTask.CurrentEnergy)
	assert.Equal(livefansboxtask.StatusFinished, param.fansRankResp.FansBox.BoxTask.Status)
	require.NotNil(param.fansRankResp.FansBox.UserTask)
	assert.Equal(1, param.fansRankResp.FansBox.UserTask.Status)
	assert.Equal("查看路径：礼物栏专属和背包", param.fansRankResp.FansBox.UserTask.PrizeTip)
	require.Len(param.fansRankResp.FansBox.UserTask.Prizes, 1)
	assert.Equal(2, param.fansRankResp.FansBox.UserTask.Prizes[0].PrizeType)
	assert.EqualValues(334455, param.fansRankResp.FansBox.UserTask.Prizes[0].PrizeElementID)
	assert.Equal("fans_box_avatar_frame", param.fansRankResp.FansBox.UserTask.Prizes[0].PrizeName)
	assert.Equal("https://static-test.missevan.com/test/fans_box.png", param.fansRankResp.FansBox.UserTask.Prizes[0].PrizeIconURL)
	assert.EqualValues(1, param.fansRankResp.FansBox.UserTask.Prizes[0].PrizeNum)

	// 测试主播侧下发下个等级宝箱信息
	param.creatorID = 12
	param.buildLiveFansBox()
	require.NotNil(param.fansRankResp.FansBox)
	require.NotNil(param.fansRankResp.FansBox.NextLevel)
	assert.Equal(livefansbox.Level3, param.fansRankResp.FansBox.NextLevel.Level)
	assert.Equal("粉丝团宝箱 3", param.fansRankResp.FansBox.NextLevel.Name)
	assert.EqualValues(21, param.fansRankResp.FansBox.NextLevel.FansNum)

	// 测试用户没有领取宝箱记录
	param.creatorID = 2333
	require.NoError(livefansboxrewardlog.LiveFansBoxRewardLog{}.DB().Delete("", "room_id = ?", testRoomID).Error)
	param.buildLiveFansBox()
	require.NotNil(param.fansRankResp.FansBox)
	require.NotNil(param.fansRankResp.FansBox.UserTask)
	assert.Equal(1, param.fansRankResp.FansBox.UserTask.Status)
	assert.Equal("", param.fansRankResp.FansBox.UserTask.PrizeTip)
	assert.Len(param.fansRankResp.FansBox.UserTask.Prizes, 0)

	// 测试用户未拥有该直播间的粉丝勋章
	_, err = livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": 12, "room_id": testRoomID})
	require.NoError(err)
	param.buildLiveFansBox()
	require.NotNil(param.fansRankResp.FansBox)
	assert.Nil(param.fansRankResp.FansBox.UserTask)

	// 测试当前宝箱为最高等级，不下发下个等级的宝箱信息
	require.NoError(livefansboxtask.LiveFansBoxTask{}.DB().Delete("", "room_id = ?", testRoomID).Error)
	testTask = &livefansboxtask.LiveFansBoxTask{
		Level:     livefansbox.Level7,
		Bizdate:   now.Format(goutil.TimeFormatYMD),
		RoomID:    testRoomID,
		FansCount: 50,
		Energy:    2333,
		Status:    livefansboxtask.StatusFinished,
	}
	require.NoError(livefansboxtask.LiveFansBoxTask{}.DB().Create(testTask).Error)
	param = &fansRankParam{
		user: &user.User{
			IUser: user.IUser{
				ID: 12,
			},
		},
		roomID: testRoomID,
	}
	param.buildLiveFansBox()
	require.NotNil(param.fansRankResp.FansBox)
	assert.Nil(param.fansRankResp.FansBox.NextLevel)

	// 测试未启用粉丝宝箱功能
	param = &fansRankParam{
		user: &user.User{
			IUser: user.IUser{
				ID: 12,
			},
		},
		roomID: testRoomID,
	}
	config.Conf.Params.MedalParams.EnableFansBox = false
	param.buildLiveFansBox()
	assert.Nil(param.fansRankResp.FansBox)
}
