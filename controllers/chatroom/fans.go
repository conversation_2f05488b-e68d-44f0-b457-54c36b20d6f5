package chatroom

import (
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansbox"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxrewardlog"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxtask"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxusertask"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const liveQQGroup int64 = 754154631

type fansRankParam struct {
	c         *handler.Context
	roomID    int64
	creatorID int64
	user      *user.User

	isTop3 bool // TODO: 目前根据 type 是否大于 0 判断的，后面需要扩展再改成记录 type

	fansBoxTask *livefansboxtask.LiveFansBoxTask
	prizes      []*liveprize.Prize

	fansRankResp
}

type fansRankElem struct {
	*livemedal.Simple

	Rank   int64            `json:"rank"`
	Titles []liveuser.Title `json:"titles,omitempty"`
}

type fansRankResp struct {
	Data            []*fansRankElem           `json:"data"`
	MyMedal         *myMedal                  `json:"my_medal,omitempty"`
	Rule            string                    `json:"rule"`
	FansCount       int64                     `json:"fans_count"`
	MedalLimit      int64                     `json:"medal_limit"`
	Gift            *gift.ObtainMedalGift     `json:"gift,omitempty"`
	MedalGift       *gift.ObtainMedalGift     `json:"medal_gift,omitempty"`
	Tasks           []*livemedal.MedalTask    `json:"tasks,omitempty"`
	NormalPrivilege []*livemedal.FanPrivilege `json:"normal_privilege,omitempty"`
	SuperPrivilege  []*livemedal.FanPrivilege `json:"super_privilege,omitempty"`
	FansBox         *fansBox                  `json:"fans_box,omitempty"` // 粉丝团宝箱信息，不下发时不展示
}

type fansBox struct {
	Level      int        `json:"level"`                // 粉丝团宝箱等级
	Name       string     `json:"name"`                 // 宝箱名称
	IconURL    string     `json:"icon_url"`             // 宝箱 icon
	RewardTip  string     `json:"reward_tip"`           // 奖励描述，主播侧和用户侧的描述有区别
	JoinEnergy int        `json:"join_energy"`          // 新加入粉丝团可贡献的能量值
	NextLevel  *nextLevel `json:"next_level,omitempty"` // 下一个等级宝箱信息，仅主播侧下发，已是最高等级时不下发
	BoxTask    boxTask    `json:"box_task"`             // 宝箱任务
	UserTask   *userTask  `json:"user_task,omitempty"`  // 用户粉丝团宝箱任务信息，仅用户是粉丝团成员时下发，主播侧不下发
}

type nextLevel struct {
	Level   int    `json:"level"`    // 粉丝团宝箱等级
	Name    string `json:"name"`     // 宝箱名称
	FansNum int64  `json:"fans_num"` // 解锁下一个等级宝箱需要的粉丝团人数
}

type boxTask struct {
	ID            int64 `json:"id"`             // 任务 ID
	TargetEnergy  int   `json:"target_energy"`  // 解锁宝箱所需能量值
	CurrentEnergy int   `json:"current_energy"` // 已达成的能量值
	Status        int   `json:"status"`         // 宝箱完成状态，0：未完成；1：已完成
}

const statusContributed = 1

type userTask struct {
	Status   int         `json:"status"`              // 任务状态，0：未助力；1：已助力
	PrizeTip string      `json:"prize_tip,omitempty"` // 奖品查看路径提示文案，仅用户已领取奖励时下发
	Prizes   []prizeInfo `json:"prizes,omitempty"`    // 奖励领取详情，仅用户已领取奖励时下发
}

type prizeInfo struct {
	PrizeType      int    `json:"prize_type"`       // 奖品类型：2：外观；4：背包礼物；10：用户定制礼物
	PrizeElementID int64  `json:"prize_element_id"` // 礼物 ID 或外观 ID
	PrizeName      string `json:"prize_name"`       // 奖品名称
	PrizeIconURL   string `json:"prize_icon_url"`   // 奖品图片
	PrizeNum       int64  `json:"prize_num"`        // 奖品数量
}

type obtainMedalGift struct {
	GiftID  int64  `json:"gift_id"`
	Name    string `json:"name"`
	Price   int64  `json:"price"` // 钻石
	IconURL string `json:"icon_url"`
}

type myMedal struct {
	*livemedal.LiveMedal
	RankUp    int64 `json:"rank_up"`
	Rank      int   `json:"rank"`
	MedalFull bool  `json:"medal_full"`
	MaxLevel  int   `json:"max_level"`
}

// ActionFansRank 粉丝榜
/**
 * @api {get} /api/v2/chatroom/fans/rank 粉丝榜
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {number=0,1} [type=0] 榜单类型, 0: 50 名排行榜, 1: top3 榜单及粉丝亲密度任务
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "my_medal": {
 *         "medal_full": false, // 勋章是否获取满了，到达数量限制为 true
 *         "max_level": 40, // 勋章等级限制
 *         "rank": 1, // 排行
 *         "rank_up": 0,  // 排行上升所需亲密度或者是获取勋章所需钻石
 *         "room_id": 123, // 房间号
 *         "creator_id": 12,
 *         "name": "勋章名称",
 *         "status": 2, // 勋章状态 0: 未获得，1: 已获得，2: 佩戴中
 *         "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *           "expire_time": 1576116700 // 秒级时间戳
 *         },
 *         "point": 1000, // 亲密度
 *         "level": 6, // 等级
 *         "level_up_point": 1500, // 等级上升所需亲密度
 *         "frame_url": "https://static-test.missevan.com/live/medalframes/3f12/level15_0_9_0_54.png", // 用户佩戴的定制粉丝徽章
 *         "user_id": 1234,
 *         "username": "用户名",
 *         "iconurl": "http://static.example.com/avatars/icon01.png",
 *         "today_threshold": 500, // 今日上限
 *         "today_point": 200, // 今日获取的亲密度
 *         "rank_invisible": false // 是否榜单隐身
 *       },
 *       "data": [
 *         {
 *           "room_id": 123,
 *           "creator_id": 12,
 *           "name": "勋章名称",
 *           "status": 2,
 *           "point": 1000,
 *           "level": 5,
 *           "level_up_point": 1000,
 *           "user_id": 12345,
 *           "username": "用户名",
 *           "iconurl": "http://static.example.com/avatars/icon01.png",
 *           "frame_url": "https://static-test.missevan.com/live/medalframes/3f12/level15_0_9_0_54.png", // 列表展示的定制粉丝徽章
 *           "titles": [
 *             {
 *               "type": "level",
 *               "level": 120,
 *               "frame_url": "https://static-test.missevan.com/live/medalframes/3f12/level15_0_9_0_54.png", // 用户佩戴的定制粉丝徽章
 *             },
 *             {
 *               "type": "identity_badge", // 身份铭牌
 *               "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *               "appearance_id": 10001 // 图标 ID
 *             }
 *           ],
 *           "rank_invisible": false // 榜单没隐身
 *         }, {
 *           "room_id": 123,
 *           "creator_id": 12,
 *           "name": "勋章名称",
 *           "status": 2,
 *           "point": 1000,
 *           "level": 5,
 *           "level_up_point": 1000,
 *           "user_id": 1234,
 *           "username": "勋章持有者和房主不修改其自身的状态",
 *           "iconurl": "http://static.example.com/avatars/icon01.png",
 *           "rank_invisible": true // 榜单隐身中
 *         }, {
 *           "room_id": 123,
 *           "creator_id": 12,
 *           "name": "勋章名称",
 *           "status": 2,
 *           "point": 1000,
 *           "level": 5,
 *           "level_up_point": 1000,
 *           "user_id": 0,
 *           "username": "神秘人",
 *           "iconurl": "http://static.example.com/avatars/inbisible.png",
 *           "rank_invisible": true // 榜单隐身中
 *         }
 *       ],
 *       "fans_box": { // 粉丝团宝箱信息，不下发时不展示
 *         "level": 1, // 粉丝团宝箱等级
 *         "name": "粉丝团 Lv1 宝箱", // 宝箱名称
 *         "icon_url": "http://static.example.com/icons/001.png", // 宝箱 icon
 *         "reward_tip": "可能开出：奖励 A、奖励 B、奖励 C", // 奖励描述，主播侧和用户侧的描述有区别
 *         "join_energy": 50, // 新加入粉丝团可贡献的能量值，固定值 50，一键加团弹窗、主播侧奖励玩法介绍等地方拼接文案时使用
 *         "next_level": { // 下一个等级宝箱信息，仅主播侧下发，已是最高等级时不下发
 *           "level": 2, // 粉丝团宝箱等级
 *           "name": "粉丝团 Lv2 宝箱", // 宝箱名称
 *           "fans_num": 1000 // 解锁下一个等级宝箱需要的粉丝团人数
 *         },
 *         "box_task": { // 宝箱任务
 *           "id": 1, // 任务 ID
 *           "target_energy": 10000, // 解锁宝箱所需能量值
 *           "current_energy": 1400, // 已达成的能量值
 *           "status": 0 // 宝箱完成状态，0：未完成；1：已完成
 *         },
 *         "user_task": { // 用户粉丝团宝箱任务信息，仅用户是粉丝团成员时下发，主播侧不下发
 *           "status": 0, // 任务状态，0：未助力；1：已助力
 *           "prizes": [ // 奖励领取详情，仅用户已领取奖励时下发，客户端展示奖励弹窗时根据 prizes 数量和 prize_type 判断是否展示立即送出按钮，
 *                       // 只有一种礼物且 prize_type 为 4（背包礼物）时才展示立即送出按钮，点击立即送出按钮时调用 /api/v2/chatroom/backpack/send 送背包礼物接口送礼
 *             {
 *               "prize_type": 2, // 奖品类型：2：外观；4：背包礼物；10：用户定制礼物
 *               "prize_element_id": 111, // 礼物 ID 或外观 ID
 *               "prize_name": "外观奖励XX头像框", // 奖品名称
 *               "prize_icon_url": "http://static-test.maoercdn.com/xxx/xxx.png", // 奖品图片
 *               "prize_num": 2 // 奖品数量
 *             }
 *           ],
 *           "prize_tip": "查看路径：xxxxxx" // 奖品查看路径提示文案
 *         }
 *       },
 *       "rule": "https://link.missevan.com/fm/fans-system-guide",
 *       "fans_count": 10,
 *       "medal_limit": 30,
 *       "medal_gift": { // 一键获取粉丝勋章的礼物信息，仅在未获得勋章时返回
 *         "gift_id": 1,
 *         "name": "礼物 A",
 *         "price": 1234, // 单位：钻
 *         "icon_url": "http://static.example.com/gift/001.png",
 *         "discount": { // 折扣信息，没有需要展示的折扣不下发此字段
 *           "label_icon_url": "http://static.example.com/gift/label.png", // 折扣角标，收到开通粉丝勋章 ws 消息时需要移除角标展示
 *           "tip": "粉团 1 折" // 获取粉丝勋章的优惠信息
 *         }
 *       },
 *       "tasks": [ // 亲密度任务，在用户拥有勋章且参数 type=1 时返回
 *         {
 *           "task_type": 1, // 客户端根据 type 确认图标和跳转方式
 *           "name": "赠送猫粮",
 *           "description": "+1 亲密度 / 1 猫粮",
 *           "status": 0 // 0 未完成, 1 已完成任务
 *         },
 *         {
 *           "task_type": 4,
 *           "name": "收听 30 分钟直播",
 *           "description": "+15 亲密度",
 *           "status": 0 // 0 未完成, 1 已完成任务
 *         },
 *         {
 *           "task_type": 6,
 *           "name": "超粉任务",
 *           "description": "+15 亲密度",
 *           "status": 0 // 0 未完成, 1 已完成任务
 *         }
 *       ],
 *       "normal_privilege": [ // 有勋章时不返回该字段
 *         {
 *           "iconurl": "http://static.example.com/privilege/000.png",
 *           "name": "粉丝勋章",
 *           "description": "粉丝专属勋章"
 *         }
 *       ],
 *       "super_privilege": [
 *         {
 *           "iconurl": "http://static.example.com/privilege/000.png",
 *           "name": "超粉勋章",
 *           "description": "超粉专属特别勋章"
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionFansRank(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newFansRankParam(c)
	if err != nil {
		return nil, err
	}
	if ok, _ := room.HaveMedal(param.roomID); ok {
		err = param.findRank()
		if err != nil {
			return nil, err
		}
		param.findMyMedal()
		param.trimRedundancy()
		param.checkRankInvisible()
		param.findRoomFansCount()
		param.buildObtainMedalGift()
		param.buildMedalTasks()
		param.buildLiveFansBox()
	}
	param.buildPrivilege()
	return param.fansRankResp, nil
}

func (param *fansRankParam) buildLiveFansBox() {
	if !livefansbox.EnableFansBox() {
		return
	}

	// 查询直播间当前粉丝团宝箱任务
	task, err := livefansboxtask.FindOrCreate(param.roomID, goutil.TimeNow())
	if err != nil {
		logger.WithField("room_id", param.roomID).Errorf("查询直播间当前粉丝团宝箱任务出错：%v", err)
		return
	}
	param.fansBoxTask = task

	// 查询粉丝团宝箱信息
	boxMap, err := livefansbox.FindLevelFansBoxMap()
	if err != nil {
		logger.Errorf("查询粉丝团宝箱信息出错：%v", err)
		return
	}
	currentBoxInfo, ok := boxMap[param.fansBoxTask.Level]
	if !ok {
		logger.WithField("level", param.fansBoxTask.Level).Error("未查询到对应等级的粉丝团宝箱信息")
		return
	}
	var nextBoxInfo *livefansbox.LiveFansBox
	if nl := param.fansBoxTask.Level + 1; nl <= livefansbox.MaxLevel && (param.user != nil && param.user.ID == param.creatorID) {
		if nextBoxInfo, ok = boxMap[nl]; !ok {
			logger.WithField("next_level", nl).Error("未查询到下个等级的粉丝团宝箱信息")
			return
		}
	}

	// 查询当前宝箱奖品信息
	prizeIDs := make([]int64, 0, len(currentBoxInfo.MoreInfo.Rewards))
	for _, reward := range currentBoxInfo.MoreInfo.Rewards {
		prizeIDs = append(prizeIDs, reward.PrizeIDs...)
	}
	if len(prizeIDs) == 0 {
		logger.WithField("level", param.fansBoxTask.Level).Error("粉丝团宝箱奖品 ID 不存在")
		return
	}
	param.prizes, err = liveprize.FindPrizes(prizeIDs)
	if err != nil {
		logger.WithFields(logger.Fields{
			"level":     param.fansBoxTask.Level,
			"prize_ids": prizeIDs,
		}).Errorf("查询当前粉丝团宝箱奖品信息出错：%v", err)
		return
	}
	if len(param.prizes) == 0 {
		logger.WithField("level", param.fansBoxTask.Level).Error("粉丝团宝箱奖品信息不存在")
		return
	}
	var prizeNames []string
	for _, prize := range param.prizes {
		prizeNames = append(prizeNames, prize.Name)
	}

	// 查询用户粉丝团宝箱任务信息
	userTaskInfo, err := param.findUserTaskInfo()
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id": param.user.ID,
			"room_id": param.roomID,
		}).Errorf("查询用户粉丝团宝箱任务信息出错，%v", err)
		return
	}

	rewardTip := "可能开出："
	if param.user != nil && param.user.ID == param.creatorID {
		rewardTip = "今日 24 点前完成宝箱任务，贡献能量的用户可能开出："
	}
	fansBoxResp := &fansBox{
		Level:      param.fansBoxTask.Level,
		Name:       currentBoxInfo.Name,
		IconURL:    currentBoxInfo.IconURL,
		RewardTip:  rewardTip + strings.Join(prizeNames, "、"),
		JoinEnergy: livefansboxtask.JoinEnergy,
	}

	if nextBoxInfo != nil {
		fansBoxResp.NextLevel = &nextLevel{
			Level:   nextBoxInfo.Level,
			Name:    nextBoxInfo.Name,
			FansNum: nextBoxInfo.FansCount,
		}
	}
	fansBoxResp.BoxTask = boxTask{
		ID:            param.fansBoxTask.ID,
		TargetEnergy:  currentBoxInfo.Energy,
		CurrentEnergy: min(param.fansBoxTask.Energy, currentBoxInfo.Energy),
		Status:        param.fansBoxTask.Status,
	}
	if userTaskInfo != nil {
		fansBoxResp.UserTask = userTaskInfo
	}
	param.FansBox = fansBoxResp
}

func (param *fansRankParam) findUserTaskInfo() (*userTask, error) {
	// 主播侧不下发
	if param.user == nil || param.user.ID == param.creatorID {
		return nil, nil
	}

	// 查询用户是否为粉丝团成员，仅用户是粉丝团成员时下发
	hasMedal, err := livemedal.HasUserOwnedMedal(param.user.ID, param.roomID)
	if err != nil {
		return nil, err
	}
	if !hasMedal {
		return nil, nil
	}

	ut, err := livefansboxusertask.FindUserTask(param.fansBoxTask.ID, param.user.ID)
	if err != nil {
		return nil, err
	}
	userTaskInfo := &userTask{}
	if ut != nil && ut.UserEnergy > 0 {
		userTaskInfo.Status = statusContributed
	}
	if userTaskInfo.Status != statusContributed || param.fansBoxTask.Status != livefansboxtask.StatusFinished {
		return userTaskInfo, nil
	}

	userRewardInfo, err := livefansboxrewardlog.FindUserFansBoxRewardLog(ut.ID)
	if err != nil {
		return nil, err
	}
	if userRewardInfo == nil {
		return userTaskInfo, nil
	}

	prizeMap := util.ToMap(param.prizes, func(p *liveprize.Prize) int64 {
		return p.ID
	})
	for _, prizeID := range userRewardInfo.MoreInfo.Reward.PrizeIDs {
		prize, ok := prizeMap[prizeID]
		if !ok {
			logger.WithField("prize_id", prizeID).Error("未查询到粉丝团宝箱奖品信息")
			continue
		}
		userTaskInfo.Prizes = append(userTaskInfo.Prizes, prizeInfo{
			PrizeType:      prize.Type,
			PrizeElementID: prize.ElementID,
			PrizeName:      prize.Name,
			PrizeIconURL:   prize.IconURL,
			PrizeNum:       prize.Num,
		})
	}
	if len(userTaskInfo.Prizes) > 0 {
		userTaskInfo.PrizeTip = livefansbox.RewardTipMap[userRewardInfo.MoreInfo.Reward.Type]
	}

	return userTaskInfo, nil
}

func newFansRankParam(c *handler.Context) (*fansRankParam, error) {
	param := &fansRankParam{user: c.User(), c: c}
	param.roomID, _ = c.GetParamInt64("room_id")
	if param.roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	fansType, _ := c.GetParamInt("type")
	param.isTop3 = fansType > 0
	var err error
	param.creatorID, err = room.FindCreatorID(param.roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.creatorID == 0 {
		return nil, actionerrors.ErrCannotFindRoom
	}
	param.Rule = config.Conf.Params.MedalParams.FanRule
	param.Data = make([]*fansRankElem, 0)
	param.MedalLimit = livemedal.UserMaxMedalCount()
	return param, nil
}

func (param *fansRankParam) findRank() (err error) {
	filter := bson.M{
		"room_id": param.roomID,
		"status":  bson.M{"$in": bson.A{livemedal.StatusOwned, livemedal.StatusShow}},
	}
	// 按照亲密度由高到低、佩戴在前排序
	limit := int64(50)
	// 使用 bson.M 有可能出现排序顺序出错
	sort := bson.D{bson.E{Key: "point", Value: -1}, bson.E{Key: "status", Value: -1}}
	opt := options.Find().SetSort(sort).SetLimit(limit)
	medals, err := livemedal.FindSimples(filter, opt, true, false)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.Data = make([]*fansRankElem, len(medals))
	userIDs := make([]int64, len(medals))
	for i := range param.Data {
		param.Data[i] = &fansRankElem{Simple: medals[i]}
		param.Data[i].Rank = int64(i) + 1
		userIDs[i] = medals[i].UserID
	}
	users, err := liveuser.SimpleSliceToMap(liveuser.ListSimples(
		bson.M{"user_id": bson.M{"$in": userIDs}}, &liveuser.FindOptions{FindTitles: true, RoomID: param.roomID},
		options.Find().SetProjection(liveuser.ProjectionTitles)))
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	invisibleUsers := userstatus.RankInvisibleUsers(param.roomID)
	for i := range param.Data {
		if u := users[param.Data[i].UserID]; u != nil {
			param.Data[i].Titles = u.Titles
		}
		_, param.Data[i].RankInvisible = invisibleUsers[param.Data[i].UserID]
	}
	return nil
}

func (param *fansRankParam) findMyMedal() {
	if param.user == nil {
		return
	}
	if param.creatorID == param.user.ID {
		return
	}
	param.MyMedal = new(myMedal)
	// 先判断是否勋章已满
	count, err := livemedal.CountNormalMedal(param.user.ID)
	if err != nil {
		logger.Error(err)
		return
	}
	uv, err := vip.UserActivatedVip(param.c.UserID(), false, param.c)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if uv != nil && int64(uv.Info.MedalNum) > param.MedalLimit {
		param.MedalLimit = int64(uv.Info.MedalNum)
	}
	param.MyMedal.MedalFull = count >= param.MedalLimit
	param.MyMedal.MaxLevel = livemedal.UserMedalLevelLimit()

	param.MyMedal.LiveMedal, err = livemedal.FindOne(bson.M{"user_id": param.user.ID, "room_id": param.roomID}, nil)
	if err != nil && err != mongo.ErrNoDocuments {
		logger.Error(err)
		// PASS
	}
	if param.MyMedal.LiveMedal == nil {
		param.MyMedal.LiveMedal = new(livemedal.LiveMedal)
		param.MyMedal.UserID = param.user.ID
		param.MyMedal.Username = param.user.Username
		param.MyMedal.IconURL = param.user.IconURL
		param.MyMedal.Status = livemedal.StatusPending // 认为是一个未获取的状态
	}
	param.MyMedal.RankInvisible = userstatus.IsRankInvisible(param.user.ID, param.roomID, true)
	// 没获得勋章直接返回
	if param.MyMedal.Status == livemedal.StatusPending {
		param.MyMedal.LevelUpPoint = livemedal.MinContribution
		param.MyMedal.RankUp = livemedal.MinContribution - param.MyMedal.Point
		pointMulti, _ := livemedal.FindMedalPointMultiple(param.creatorID, livemedal.IsSuperFanActive(param.MyMedal.LiveMedal.SuperFan))
		// 亲密度倍数处理
		if pointMulti != 1 {
			// LevelUpPoint 表示需要共需要多少亲密度可以获得勋章
			// Point 表示当前已经获得了多少亲密度
			// 未开启亲密度倍数时，客户端显示的需要消费 param.MyMedal.LevelUpPoint - param.MyMedal.Point 个钻石可获得勋章
			// 如果当前直播间满足亲密度倍数的条件，即用户消费 1 钻，可获得该主播的 n 亲密度
			param.MyMedal.LevelUpPoint /= pointMulti
			param.MyMedal.Point /= pointMulti
			param.MyMedal.RankUp = param.MyMedal.LevelUpPoint - param.MyMedal.Point
		}
		return
	}
	// 用户在榜内，计算排行，同步榜单隐身状态后返回
	for i := 0; i < len(param.Data); i++ {
		if param.Data[i].UserID == param.user.ID {
			param.Data[i].RankInvisible = param.MyMedal.RankInvisible
			param.MyMedal.Rank = i + 1
			if i != 0 {
				param.MyMedal.RankUp = param.Data[i-1].Point - param.MyMedal.Point + 1
			}
			return
		}
	}
	// 用户在榜外
	last := param.Data[len(param.Data)-1]
	param.MyMedal.RankUp = last.Point - param.MyMedal.Point + 1
	if param.MyMedal.RankUp < 0 {
		param.MyMedal.RankUp = 0
	}
}

func (param *fansRankParam) trimRedundancy() {
	if param.isTop3 && len(param.Data) > 3 {
		param.Data = param.Data[:3]
	}
}

func (param *fansRankParam) checkRankInvisible() {
	if len(param.Data) == 0 {
		return
	}
	creatorID := param.Data[0].CreatorID
	var userID int64
	if param.user != nil {
		userID = param.user.ID
	}
	checkRankInvisible(creatorID, userID, false, nil, nil, param.Data)
}

func (param *fansRankParam) findRoomFansCount() {
	var err error
	param.FansCount, err = livemedal.CountRoomMedal(param.roomID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *fansRankParam) buildObtainMedalGift() {
	if param.MyMedal != nil && param.MyMedal.Status > livemedal.StatusPending {
		return
	}
	if param.creatorID == param.c.UserID() {
		// 主播在自己的直播间不需要下发一键获取粉丝勋章礼物的信息
		return
	}
	param.fansRankResp.MedalGift = gift.FindObtainMedalGift(param.creatorID, param.c.UserID(), param.c.Equip())
	// WORKAROUND: 客户端新版本使用 MedalGift，更新版本待定
	param.fansRankResp.Gift = param.fansRankResp.MedalGift
}

func (param *fansRankParam) buildMedalTasks() {
	if !param.isTop3 || param.user == nil || param.creatorID == param.user.ID {
		// 仅在获取 top3 榜单，用户登录状态下且非当前直播间主播时才需要返回勋章任务
		return
	}
	if param.MyMedal == nil || param.MyMedal.Status <= livemedal.StatusPending {
		return
	}
	param.fansRankResp.Tasks = livemedal.MedalTasks() // 数据查询失败，降级为都未完成
	lu, err := liveuser.FindOneLiveUser(bson.M{"room_id": param.roomID, "user_id": param.user.ID})
	if err != nil {
		logger.Error(err)
		return
	}
	st := util.BeginningOfDay(goutil.TimeNow())
	catFoodFilter := bson.M{
		"user_id":   param.user.ID,
		"room_id":   param.roomID,
		"gift_id":   useritems.GiftIDCatFood,
		"sent_time": bson.M{"$gte": st},
	}
	if param.MyMedal.CreatedTime.After(st) {
		// 获得勋章后的送礼才能计入任务
		// FIXME: livemedal.CreatedTime 有可能为 pending 状态时的勋章获取时间，与实际拥有勋章时间不同
		catFoodFilter["sent_time"] = bson.M{"$gte": param.MyMedal.CreatedTime}
	}
	lg, err := livegifts.FindOne(catFoodFilter)
	if err != nil {
		logger.Error(err)
		return
	}
	for i := range param.fansRankResp.Tasks {
		switch param.fansRankResp.Tasks[i].TaskType {
		case livemedal.TaskTypeSendCatFood:
			param.fansRankResp.Tasks[i].Status = goutil.BoolToInt(lg != nil)
		case livemedal.TaskTypeShareRoom:
			param.fansRankResp.Tasks[i].Status = goutil.BoolToInt(lu != nil && lu.StatusShare != livemedal.ShareStatusPending &&
				goutil.TimeGte(lu.TimeShare, st))
		case livemedal.TaskTypeOnline6Minute:
			param.fansRankResp.Tasks[i].Status = goutil.BoolToInt(lu != nil && goutil.TimeGte(lu.TodayTimeOnline, st) &&
				lu.TodayAcqOnline >= 6*1000*util.SecondOneMinute)
		case livemedal.TaskTypeOnline30Minute:
			param.fansRankResp.Tasks[i].Status = goutil.BoolToInt(lu != nil && goutil.TimeGte(lu.TodayTimeOnline, st) &&
				lu.TodayAcqOnline >= 30*1000*util.SecondOneMinute)
		case livemedal.TaskTypeRegisterSuperFan:
			param.fansRankResp.Tasks[i].Status = goutil.BoolToInt(livemedal.IsSuperFanActive(param.MyMedal.SuperFan))
		}
	}
}

func (param *fansRankParam) buildPrivilege() {
	// WORKAROUND: 兼容 Android <= 5.7.8 的版本传错 type 的问题
	if !param.c.Equip().IsOldApp(goutil.AppVersions{IOS: "", Android: "5.7.9"}) {
		if !param.isTop3 {
			// 仅在获取 top3 榜单时才需要返回贵族特权
			return
		}
	}

	hasMedal := param.MyMedal != nil && param.MyMedal.Status > livemedal.StatusPending
	if !hasMedal {
		// 用户未拥有当前房间勋章时，需要返回普通粉丝特权
		param.NormalPrivilege = livemedal.NormalPrivilege()
	}
	param.SuperPrivilege = livemedal.SuperPrivilege(hasMedal)
}

type fansProgressResp struct {
	Medal     *room.Medal `json:"medal,omitempty"`
	Rule      string      `json:"rule"`
	Revenue   int64       `json:"revenue"`
	Threshold int64       `json:"threshold"`
	Contact   struct {
		QQGroup int64 `json:"qq_group"`
	} `json:"contact"`
}

// ActionFansProgress 粉丝勋章进度
/**
 * @api {get} /api/v2/chatroom/fans/progress 粉丝勋章进度
 * @apiDescription 通过房间 ID 或主播 ID 获取直播间的粉丝勋章进度
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} [room_id] 房间号
 * @apiParam {Number} [creator_id] 主播 ID // 房间 ID 和主播 ID 任选其一，两个参数都传，优先按房间 ID 查询
 *
 * @apiSuccess {Number} threshold 阈值，revenue 大于等于该值则说明可以获取勋章
 * @apiSuccess {Object} medal 勋章，获取了则有，未获取则无
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "medal": {
 *         "name": "喵喵喵"
 *       },
 *       "revenue": 30000,
 *       "threshold": 30000,
 *       "rule": "https://link.missevan.com/fm/fans-system-guide",
 *       "contact": {
 *         "qq_group": 2352127539,  // 现在返回的是 qq 号
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionFansProgress(c *handler.Context) (handler.ActionResponse, error) {
	roomID, _ := c.GetParamInt64("room_id")
	CreatorID, _ := c.GetParamInt64("creator_id")
	opt := &room.FindOptions{Projection: bson.M{"statistics.revenue": 1, "medal": 1}, DisableAll: true}
	// 房间 ID 和主播 ID 任选其一，两个参数都传，优先按房间 ID 查询
	var filter bson.M
	if roomID > 0 {
		filter = bson.M{"room_id": roomID}
	} else if CreatorID > 0 {
		filter = bson.M{"creator_id": CreatorID}
	} else {
		return nil, actionerrors.ErrParams
	}

	r, err := room.FindOne(filter, opt)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}

	resp := &fansProgressResp{
		Rule:      config.Conf.Params.MedalParams.FanRule,
		Threshold: livemedal.RevenueThreshold,
		Medal:     r.Medal,
		Revenue:   r.Statistics.Revenue,
	}
	resp.Contact.QQGroup = liveQQGroup
	if resp.Revenue > resp.Threshold {
		resp.Revenue = resp.Threshold
	}
	return resp, nil
}
