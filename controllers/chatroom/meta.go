package chatroom

import (
	"encoding/json"
	"math"
	"slices"
	"sort"
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	confparam "github.com/MiaoSiLa/live-service/config/params"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/redpacket"
	"github.com/MiaoSiLa/live-service/models/blackcard/liveblackcardgiftupgrade"
	"github.com/MiaoSiLa/live-service/models/blackcard/liveuserblackcard"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livedb/livegiftupgrade"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/livedb/liveroomtagrecord"
	"github.com/MiaoSiLa/live-service/models/livedb/livetagcontrollist"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/livevote"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	liveredpacket "github.com/MiaoSiLa/live-service/models/mongodb/redpacket"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	application "github.com/MiaoSiLa/live-service/models/mysql/liveapplication"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansbox"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxtask"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/redis/interaction"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// chatroom/meta 响应控制
const (
	metaTypeAll = iota
	metaTypeGifts
	metaTypeMedal
	metaTypeInteraction
	metaTypeEvents
	metaTypeHourRank
	metaTypePopups
	metaTypeGashapon
	metaTypePK
	metaTypeGiftWall
	metaTypeRedPacket
	metaTypeRedeemShop
	metaTypePia
	metaTypeMultiCombo
	metaTypeShortcutGift
	metaTypeMembers
	metaTypeConfig
	metaTypeLuckyBag
	metaTypeWishList
	metaTypeLuckyBox
	metaTypeMultiConnect // 主播连线
	metaTypeFansBox

	metaTypeLimit // 位数不会大于此值
)

const (
	giftButtonTypeURL = iota + 1 // 跳转按钮
)

// pia 戏状态
const (
	piaStatusNotStarted = iota // pia 戏未开始
	piaStatusOngoing           // pia 戏进行中
)

// Interaction 互动
type Interaction struct {
	Vote *livevote.Vote `json:"vote,omitempty"` // 直播间礼物投票
}

// NewPopups 直播间小窗新结构
type NewPopups struct {
	Fold bool                                     `json:"fold"` // 是否默认折叠
	Data []*liverecommendedelements.PopupRespItem `json:"data"` // 小窗数据
}

type pkRoomInfo struct {
	RoomID          int64       `json:"room_id"`
	CreatorID       int64       `json:"creator_id"`
	CreatorUsername string      `json:"creator_username"`
	CreatorIconURL  string      `json:"creator_iconurl"`
	Statistics      *statistics `json:"statistics,omitempty"`
}

type pkInvitation struct {
	MatchID          string      `json:"match_id"`
	Invited          int         `json:"invited"` // 0: 邀请方; 1: 被邀请方
	Duration         int64       `json:"duration"`
	RemainDuration   int64       `json:"remain_duration"`
	FightingDuration int64       `json:"fighting_duration"`
	CreateTime       int64       `json:"create_time"`
	FromRoom         *pkRoomInfo `json:"from_room,omitempty"` // 邀请方直播间信息
	ToRoom           *pkRoomInfo `json:"to_room,omitempty"`   // 被邀请方直播间信息
}

// PK 直播
type PK struct {
	Show       *int           `json:"show,omitempty"`       // PK 入口是否展示直播, 零或该字段不存在时无直播入口, 并且仅当主播在自己直播间时会下发
	IconURL    string         `json:"icon_url,omitempty"`   // PK 入口图标
	Detail     *livepk.LivePK `json:"detail,omitempty"`     // PK 详情
	Invitation *pkInvitation  `json:"invitation,omitempty"` // 指定 PK 邀请信息, 该字段中的房间信息总是对方直播间信息, 通过 invited 字段区分【自己直播间】是邀请方还是被邀请方
}

type multiConnectRoomStatus struct {
	Attention bool `json:"attention"`
}

type multiConnectRoomInfo struct {
	RoomID          int64                  `json:"room_id"`
	CreatorID       int64                  `json:"creator_id"`
	CreatorUsername string                 `json:"creator_username"`
	CreatorIconURL  string                 `json:"creator_iconurl"`
	Status          multiConnectRoomStatus `json:"status"`
}

type multiConnectMemberInfo struct {
	Index int                  `json:"index"`
	Role  int                  `json:"role"`
	Score int64                `json:"score"`
	Room  multiConnectRoomInfo `json:"room"`
}

type multiConnectUserRank struct {
	Granted int `json:"granted"` // 0: 未授权; 1: 已授权
	Enable  int `json:"enable"`  // 0: 未开启; 1: 已开启
}

type multiConnectConfig struct {
	UserRank multiConnectUserRank `json:"user_rank"`
}

type multiConnectDetail struct {
	GroupID       int64                    `json:"group_id"`
	Status        int                      `json:"status"`
	Duration      int64                    `json:"duration"`
	MuteRoomIDs   []int64                  `json:"mute_room_ids"`
	MicOffRoomIDs []int64                  `json:"mic_off_room_ids"`
	Config        multiConnectConfig       `json:"config"`
	Members       []multiConnectMemberInfo `json:"members"`
}

// MultiConnect 主播连线
type MultiConnect struct {
	Show    *int                `json:"show,omitempty"`     // 是否展示主播连线入口，0 或该字段不存时无主播连线入口，仅主播在自己直播间时会下发
	IconURL string              `json:"icon_url,omitempty"` // 入口图标
	Detail  *multiConnectDetail `json:"detail,omitempty"`
}

// RedPacket 礼物红包
type RedPacket struct {
	Name          string            `json:"name"`
	Price         int64             `json:"price,omitempty"`
	IconURL       string            `json:"icon_url"`
	BigIconURL    string            `json:"big_icon_url"`
	IconActiveURL string            `json:"icon_active_url,omitempty"`
	ImageURL      string            `json:"image_url"`
	CornerIconURL string            `json:"corner_icon_url,omitempty"`
	Intro         string            `json:"intro,omitempty"`
	IntroIconURL  string            `json:"intro_icon_url,omitempty"`
	IntroOpenURL  string            `json:"intro_open_url,omitempty"`
	Position      int               `json:"position,omitempty"` // 礼物位置
	List          []*redpacket.Elem `json:"list"`               // 房间内红包列表，为空时不显示红包领取入口
}

// RedeemShop 兑换商城
type RedeemShop struct {
	Name       string `json:"name"`
	IconURL    string `json:"icon_url"`
	WebIconURL string `json:"web_icon_url"`
	ShopURL    string `json:"shop_url"`
}

// Pia pia 戏
type Pia struct {
	Name     string `json:"name"`
	Status   int    `json:"status"`
	Duration int64  `json:"duration,omitempty"`
}

type multiComboGift struct {
	livegifts.Combo `json:",inline"`

	Gift gift.NotifyGift `json:"gift"`
}

type multiCombo struct {
	Status int               `json:"status"`
	Combos []*multiComboGift `json:"combos,omitempty"`
}

type metaResp struct {
	roomID   int64
	room     *room.Room
	metaType int
	userID   int64
	equip    *goutil.Equipment
	uvMap    map[int]*vip.UserVip
	// 二级分区和对应的一级分区，key 是二级分区 ID，value 是该二级分区对应的一级分区的 ID
	subCatalogMap map[int64]int64
	allShowTags   *tag.ShowLiveTags

	multiComboParams *params.MultiCombo // 一起送配置

	Gifts         []*metaGiftsElem                `json:"gifts,omitempty"`
	Medal         *livemedal.LiveMedal            `json:"medal,omitempty"`
	Interaction   *Interaction                    `json:"interaction,omitempty"`
	PK            *PK                             `json:"pk,omitempty"`
	MultiConnect  *MultiConnect                   `json:"multi_connect,omitempty"`
	Events        []liverecommendedelements.Event `json:"events,omitempty"`
	HourRank      *hourRank                       `json:"hour_rank,omitempty"`
	Popups        interface{}                     `json:"popups,omitempty"`
	Gashapon      *gashapon                       `json:"gashapon,omitempty"`
	GiftWall      *giftwall.ActivatedDetail       `json:"gift_wall,omitempty"`
	RedPacket     *RedPacket                      `json:"red_packet,omitempty"`
	RedeemShop    *RedeemShop                     `json:"redeem_shop,omitempty"`
	PrivilegeShop *RedeemShop                     `json:"privilege_shop,omitempty"`
	Pia           *Pia                            `json:"pia,omitempty"`
	MultiCombo    *multiCombo                     `json:"multi_combo,omitempty"`
	ShortcutGift  *gift.ShortcutGift              `json:"shortcut_gift,omitempty"`
	Members       *metaMembers                    `json:"members,omitempty"`
	Config        *metaConfig                     `json:"config,omitempty"`

	LuckyBagEntry *luckyBagEntry            `json:"lucky_bag_entry,omitempty"`
	LuckyBag      *luckybag.RoomMetaMessage `json:"lucky_bag,omitempty"`

	WishListEntry *wishListEntry `json:"wish_list,omitempty"`
	LuckyBox      *luckyBox      `json:"lucky_box,omitempty"`
	FansBox       *fansBoxEntry  `json:"fans_box,omitempty"`
}

type fansBoxEntry struct {
	Name    string  `json:"name"`     // 宝箱名称
	IconURL string  `json:"icon_url"` // 宝箱 icon
	BoxTask boxTask `json:"box_task"` // 宝箱任务
}

type metaGiftsElem struct {
	Title string      `json:"title"`
	Data  []*metaGift `json:"data"`
}

type metaGift struct {
	gift.Gift

	SpecialType string                                  `json:"special_type,omitempty"` // 用于红包、宝盒等玩法
	PriceType   int                                     `json:"price_type,omitempty"`   // 0: 固定价格 1: 最低价格
	Button      *metaGiftButton                         `json:"button,omitempty"`
	Upgrade     *livegiftupgrade.UpgradeInfo            `json:"upgrade,omitempty"`
	BlackCard   *liveblackcardgiftupgrade.BlackCardInfo `json:"black_card,omitempty"`

	OpenURL string             `json:"open_url,omitempty"`
	Buff    *gift.GashaponBuff `json:"buff,omitempty"`

	position int // 用于红包、宝盒等玩法排序使用
}

type metaMembers struct {
	Admin []metaMemberElem `json:"admin"`
	Mute  []metaMemberElem `json:"mute"`
}

type metaMemberElem struct {
	RoomID   int64  `json:"room_id"`
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	IconURL  string `json:"iconurl"`
	ExpireAt int64  `json:"expire_at,omitempty"`
}

type metaConfig struct {
	AllowHideGiftEffect bool `json:"allow_hide_gift_effect,omitempty"`
	// TODO: 考虑将 site/config live_check_push_status 中，进入直播间内的推送提示开关迁移到该处下发
}

type luckyBagEntry struct {
	Name string `json:"name"`
}

type wishListEntry struct {
	Name string `json:"name"`
}

type luckyBox struct {
	Name         string `json:"name"`
	ImageURL     string `json:"image_url"`
	LabelIconURL string `json:"label_icon_url"`
}

// 礼物类型
const (
	typeNoble7BaseGift     = iota // 神话模版礼物
	typeNoble7CustomGift          // 神话定制礼物
	typeHighnessCustomGift        // 上神定制礼物
	typeHighnessNobleGift         // 普通上神礼物
	typeOtherGift
)

func metaGiftType(g gift.Gift) int {
	if g.Type == gift.TypeNoble && g.NobleLevel == vip.NobleLevel7 {
		return typeNoble7BaseGift
	}
	if g.Type == gift.TypeNoble && g.VipType == gift.VipTypeLiveHighness {
		return typeHighnessNobleGift
	}
	if g.BaseGiftID != 0 && g.Type == gift.TypeCustom && g.VipType == gift.VipTypeLiveNoble && g.NobleLevel == vip.NobleLevel7 {
		return typeNoble7CustomGift
	}
	if g.Type == gift.TypeCustom && g.VipType == gift.VipTypeLiveHighness {
		return typeHighnessCustomGift
	}
	return typeOtherGift
}

// newMetaGift 返回带按钮的礼物和需要隐藏的礼物 ID（礼物 ID 不为 0，总是隐藏）
// TODO: 处理普通贵族礼物，另外所有礼物需要下发完整的跳转地址，供贵族详情页定位 tab 使用
func (resp metaResp) newMetaGift(g gift.Gift) (*metaGift, int64) {
	gType := metaGiftType(g)
	metaGift := &metaGift{Gift: g}
	if gType == typeOtherGift {
		return metaGift, g.BaseGiftID
	}
	// vipStatus: [上神贵族状态, 神话贵族状态]
	vipStatus := [2]int{vip.NobleStatusNone, vip.NobleStatusNone}
	var nobleLevel int
	if len(resp.uvMap) != 0 {
		highness := resp.uvMap[vip.TypeLiveHighness]
		if highness != nil {
			if highness.IsActive() {
				vipStatus[0] = vip.NobleStatusNoble
			} else if highness.BeforeRenewDeadline() {
				vipStatus[0] = vip.NobleStatusProtected
			}
		}
		noble := resp.uvMap[vip.TypeLiveNoble]
		if noble != nil {
			if noble.IsActive() {
				nobleLevel = noble.Level
				vipStatus[1] = vip.NobleStatusNoble
			} else if noble.BeforeRenewDeadline() {
				nobleLevel = noble.Level
				vipStatus[1] = vip.NobleStatusProtected
			}
		}
		trialNoble := resp.uvMap[vip.TypeLiveTrialNoble]
		if trialNoble != nil && trialNoble.IsActive() {
			nobleLevel = trialNoble.Level // 体验贵族等级一定大于等于普通贵族等级
			vipStatus[1] = vip.NobleStatusNoble
		}
	}
	customButton := &metaGiftButton{
		Text: "去定制",
		Type: giftButtonTypeURL,
		URL:  config.Conf.Params.NobleParams.CustomNobleGiftURL,
	}
	openButton := &metaGiftButton{
		Text: "开通贵族",
		Type: giftButtonTypeURL,
		URL:  config.Conf.Params.NobleParams.BuyNobleURL,
	}
	upgradeButton := &metaGiftButton{
		Text: "升级贵族",
		Type: giftButtonTypeURL,
		URL:  config.Conf.Params.NobleParams.BuyNobleURL,
	}
	renewButton := &metaGiftButton{
		Text: "续费贵族",
		Type: giftButtonTypeURL,
		URL:  config.Conf.Params.NobleParams.BuyNobleURL,
	}

	// 按钮规则: https://info.missevan.com/pages/viewpage.action?pageId=50727430
	switch vipStatus[0] {
	case vip.NobleStatusProtected: // 上神续费保护期
		if gType == typeHighnessNobleGift {
			metaGift.Button = renewButton
			return metaGift, g.BaseGiftID
		}
		if !(vipStatus[1] == vip.NobleStatusNoble && nobleLevel >= vip.NobleLevel7 && gType == typeNoble7CustomGift) &&
			(g.Type == typeNoble7CustomGift || g.Type == typeHighnessCustomGift) {
			// 在上神续费保护期中，除了神话生效情况下的神话定制礼物，其余贵族状态下的神话定制礼物和上神定制礼物返回续费按钮
			metaGift.Button = renewButton
		}
		return metaGift, g.BaseGiftID
	case vip.NobleStatusNone: // 上神失效
		if gType == typeHighnessCustomGift {
			return nil, 0
		}
		switch vipStatus[1] {
		case vip.NobleStatusNoble:
			if gType == typeHighnessNobleGift {
				metaGift.Button = upgradeButton
				return metaGift, g.BaseGiftID
			}
			// 神话以下贵族神话模版礼物显示去订制
			if gType == typeNoble7BaseGift && nobleLevel < vip.NobleLevel7 {
				metaGift.Button = customButton
				return metaGift, g.BaseGiftID
			}
		case vip.NobleStatusProtected:
			if gType == typeHighnessNobleGift {
				metaGift.Button = openButton
				return metaGift, g.BaseGiftID
			}
			if nobleLevel < vip.NobleLevel7 {
				// 神话以下贵族神话模版礼物显示去订制
				if gType == typeNoble7BaseGift {
					metaGift.Button = customButton
					return metaGift, g.BaseGiftID
				}
				// 神话以下贵族神话定制礼物不显示
				if gType == typeNoble7CustomGift {
					return nil, 0
				}
			}
			metaGift.Button = renewButton
			return metaGift, g.BaseGiftID
		case vip.NobleStatusNone:
			if gType == typeHighnessNobleGift {
				metaGift.Button = openButton
				return metaGift, g.BaseGiftID
			}
			// 贵族过期，不返回神话定制礼物
			if gType == typeNoble7CustomGift {
				return nil, 0
			}
			if gType == typeNoble7BaseGift {
				metaGift.Button = customButton
			}
		}
	}
	return metaGift, g.BaseGiftID
}

type metaGiftButton struct {
	Type int    `json:"type"`
	Text string `json:"text"`
	URL  string `json:"url,omitempty"`
}

type hourRank struct {
	Rank   int64 `json:"rank"`
	RankUp int64 `json:"rank_up"`
}

type gashapon struct {
	DefaultIconURL string             `json:"default_icon_url,omitempty"`
	LabelIconURL   string             `json:"label_icon_url,omitempty"`
	Buff           *gift.GashaponBuff `json:"buff,omitempty"`
	OpenURL        string             `json:"open_url,omitempty"`
}

func (resp *metaResp) load(c *handler.Context) (err error) {
	resp.roomID, _ = c.GetParamInt64("room_id")
	if resp.roomID <= 0 {
		return actionerrors.ErrParams
	}
	opt := &room.FindOptions{
		FindCreator: true,
	}
	resp.room, err = room.Find(resp.roomID, opt)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if resp.room == nil {
		return actionerrors.ErrCannotFindRoom
	}
	// WORKAROUND: 兼容 iOS < 4.9.1 使用位判断
	if c.Equip().IsAppOlderThan("4.9.1", "") {
		bit, _ := c.GetParamInt("type")
		if bit > math.MaxInt16 || bit < math.MinInt16 {
			return actionerrors.ErrParams
		}
		// NOTICE: 目前老版本仅 PK 的位在使用
		if goutil.BitMask(bit).IsSet(metaTypePK) {
			resp.metaType = metaTypePK
		} else {
			resp.metaType = metaTypeAll
		}
	} else {
		resp.metaType, _ = c.GetParamInt("type")
		if !c.Equip().FromApp && goutil.BitMask(resp.metaType).IsSet(metaTypePK) {
			// 兼容 Web 使用 PK 的位来判断
			resp.metaType = metaTypePK
		}
		if resp.metaType >= metaTypeLimit || resp.metaType < metaTypeAll {
			return actionerrors.ErrParams
		}
	}

	resp.equip = c.Equip()
	resp.userID = c.UserID()
	return nil
}

// hasType 响应控制
func (resp *metaResp) hasType(metaType int) bool {
	return resp.metaType == metaType || resp.metaType == metaTypeAll
}

// sortGifts 排序各礼物栏内的礼物
func sortGifts(metaGifts []*metaGiftsElem) {
	for tab, giftsElem := range metaGifts {
		gifts := giftsElem.Data
		switch tab {
		case gift.TabGiftNormal, gift.TabGiftMedal:
			// 按照 order 从小到大排序
			sort.Slice(gifts, func(i, j int) bool {
				return gifts[i].Order < gifts[j].Order
			})
		case gift.TabGiftExclusive:
			// 专属礼物排序规则：个人专属礼物 > 直播间专属礼物，专属礼物之间使用礼物 order 字段排序
			sort.Slice(gifts, func(i, j int) bool {
				priority := func(s *metaGift) int {
					switch {
					case s.Type == gift.TypeCustom || s.Type == gift.TypeBlackCard ||
						(s.Type == gift.TypeDrawSend && s.Exclusive == gift.GiftExclusiveUser):
						return 1 // 个人专属礼物优先级最高
					case s.Type == gift.TypeRoomCustom || s.Type == gift.TypeDrawSend && s.Exclusive == gift.GiftExclusiveRoom:
						return 2 // 直播间专属礼物优先级次高
					default:
						return 3 // 其它优先级最低
					}
				}
				pi, pj := priority(gifts[i]), priority(gifts[j])
				if pi != pj {
					return pi < pj
				}
				return gifts[i].Order < gifts[j].Order
			})
		default:
			// 不匹配的 tab 无需处理
		}
	}
}

func (resp *metaResp) findGifts(c *handler.Context) error {
	if !resp.hasType(metaTypeGifts) {
		return nil
	}
	gifts, err := gift.FindAllShowingGifts()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	elemPre := []*metaGiftsElem{
		{Title: "礼物"},
		{Title: "贵族"},
		{Title: "专属"},
		{Title: "粉丝"},
	}
	// 从 elemPre 构造响应的礼物，筛掉空礼物列表
	defer func() {
		sortGifts(elemPre)
		if resp.equip.IsOldApp(goutil.AppVersions{IOS: "6.0.1", Android: "6.0.1"}) {
			// WORKAROUND: iOS < 6.0.1, 安卓 < 6.0.1 的版本礼物半窗不展示专属礼物栏，用户专属和直播间专属礼物依然放到普通礼物栏
			// 定制专属礼物本身就排序在普通礼物前，所以可以直接 append
			elemPre[gift.TabGiftNormal].Data = append(elemPre[gift.TabGiftExclusive].Data, elemPre[gift.TabGiftNormal].Data...)
			elemPre[gift.TabGiftExclusive].Data = nil
		}
		resp.Gifts = make([]*metaGiftsElem, 0, len(elemPre))
		for i := range elemPre {
			if len(elemPre[i].Data) != 0 {
				resp.Gifts = append(resp.Gifts, elemPre[i])
			}
		}
		err = resp.findInteractiveGifts() // 寻找玩法礼物
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}()

	optionUser := new(gift.GroupGiftsOptionsUser)
	if resp.userID != 0 {
		user, err := liveuser.Find(resp.userID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if user == nil {
			return actionerrors.ErrCannotFindUser
		}
		optionUser.UserID = user.UserID()
		optionUser.UserLevel = usercommon.Level(user.Contribution)
	}
	giftShow, _ := gift.GroupGifts(gifts, gift.GroupGiftsOptions{
		User:           optionUser,
		RoomID:         util.NewInt64(resp.roomID),
		HideLuckyGifts: !util.IntToBool(resp.room.Status.Open), // 关播直播间需要隐藏随机礼物
	})

	if resp.userID != 0 {
		resp.uvMap, err = vip.UserVipInfos(resp.userID, false, c)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}

	err = resp.findMultiComboParams()
	if err != nil {
		logger.WithField("room_id", resp.roomID).Error(err)
		// PASS
	}
	beforeHighnessVersion := c.Equip().IsAppOlderThan("4.7.5", "5.6.3")
	beforeMultiComboVersion := c.Equip().IsOldApp(goutil.AppVersions{IOS: "6.0.7", Android: "6.0.7"})
	beforeBlackCardVersion := c.Equip().IsOldApp(goutil.AppVersions{IOS: "6.3.8", Android: "6.3.8"})
	enableSendMultiCombo := resp.multiComboParams != nil && resp.multiComboParams.EnableSend(resp.roomID)
	var removeIDs []int64
	for i := range giftShow {
		elemPre[i].Data = make([]*metaGift, 0, len(giftShow[i]))
		for _, g := range giftShow[i] {
			// WORKAROUND: iOS < 4.7.5，安卓 < 5.6.3 礼物列表不展示上神定制礼物
			if g.VipType == gift.VipTypeLiveHighness && beforeHighnessVersion {
				continue
			}
			// WORKAROUND: iOS < 6.0.7，安卓 < 6.0.7 礼物列表不展示直播间连击礼物
			if g.Comboable == gift.ComboableTypeMulti {
				if !enableSendMultiCombo || beforeMultiComboVersion {
					continue
				}
			}
			// WORKAROUND: web 端，鸿蒙客户端，iOS < 6.3.8，安卓 < 6.3.8 礼物列表不展示黑卡钻石皮肤礼物
			if g.Type == gift.TypeBlackCard &&
				(!c.Equip().FromApp || c.Equip().OS == goutil.HarmonyOS || beforeBlackCardVersion) {
				continue
			}

			mg, removeID := resp.newMetaGift(g)
			if mg != nil {
				elemPre[i].Data = append(elemPre[i].Data, mg)
			}
			if removeID != 0 {
				removeIDs = append(removeIDs, removeID)
			}
		}
	}

	if len(removeIDs) > 0 {
		// 移除需要移除的原型礼物
		for i, item := range elemPre {
			arr := make([]*metaGift, 0, len(item.Data))
			for _, g := range item.Data {
				if slices.Contains(removeIDs, g.GiftID) {
					continue
				}
				arr = append(arr, g)
			}
			elemPre[i].Data = arr
		}
	}

	err = resp.buildUpgradeGifts(elemPre, gifts)
	if err != nil {
		return err
	}
	err = resp.buildBlackCardGifts(elemPre, gifts)
	if err != nil {
		return err
	}
	return nil
}

func (resp *metaResp) buildBlackCardGifts(giftElems []*metaGiftsElem, allShowGifts []gift.Gift) (err error) {
	blackCardGiftLevelMap := make(map[int64]int, len(giftElems[gift.TabGiftNormal].Data))
	introOpenURL := confparam.BlackCardURL(true)
	defer func() {
		if err != nil || len(giftElems[gift.TabGiftExclusive].Data) == 0 {
			return
		}
		userBlackCardLevel := 0
		if resp.userID != 0 {
			userBlackCardInfo, deferErr := liveuserblackcard.FindUserActiveBlackCard(resp.userID)
			if deferErr != nil {
				err = actionerrors.NewErrServerInternal(deferErr, nil)
				return
			}
			if userBlackCardInfo != nil {
				userBlackCardLevel = userBlackCardInfo.Level
			}
		}
		updatedGifts := make([]*metaGift, 0, len(giftElems[gift.TabGiftExclusive].Data))
		for _, g := range giftElems[gift.TabGiftExclusive].Data {
			if g.Type == gift.TypeBlackCard {
				level, ok := blackCardGiftLevelMap[g.GiftID]
				if !ok {
					logger.WithField("gift_id", g.GiftID).Error("黑卡专属礼物在礼物 tab 栏中不存在")
					continue
				}
				if userBlackCardLevel >= level {
					g.BlackCard = &liveblackcardgiftupgrade.BlackCardInfo{
						Level: level,
					}
					g.IntroOpenURL = introOpenURL
					updatedGifts = append(updatedGifts, g)
				}
			} else {
				updatedGifts = append(updatedGifts, g)
			}
		}
		giftElems[gift.TabGiftExclusive].Data = updatedGifts
	}()

	blackCardBaseGiftIDs := make([]int64, 0, len(giftElems[gift.TabGiftNormal].Data))
	blackCardBaseGiftMap := make(map[int64]*metaGift, len(giftElems[gift.TabGiftNormal].Data))
	for _, g := range giftElems[gift.TabGiftNormal].Data {
		if g.IsBlackCardBaseGift() {
			g.IntroOpenURL = introOpenURL
			blackCardBaseGiftIDs = append(blackCardBaseGiftIDs, g.GiftID)
			blackCardBaseGiftMap[g.GiftID] = g
		}
	}
	if len(blackCardBaseGiftMap) == 0 {
		return nil
	}

	blackCardGiftUpgrades, err := liveblackcardgiftupgrade.ListBlackCardGiftUpgrades(blackCardBaseGiftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(blackCardGiftUpgrades) == 0 {
		return nil
	}

	showGiftsMap := util.ToMap(allShowGifts, func(g gift.Gift) int64 {
		return g.GiftID
	})
	for _, giftUpgradeInfo := range blackCardGiftUpgrades {
		blackCardGift, ok := showGiftsMap[giftUpgradeInfo.BlackCardGiftID]
		if !ok {
			continue
		}
		if baseGift, ok := blackCardBaseGiftMap[giftUpgradeInfo.BaseGiftID]; ok {
			baseGift.BlackCard = &liveblackcardgiftupgrade.BlackCardInfo{
				Level:    giftUpgradeInfo.Level,
				BaseGift: liveblackcardgiftupgrade.NewGiftInfo(baseGift.Gift, giftUpgradeInfo.BaseGiftSmallIconURL),
				Gift:     liveblackcardgiftupgrade.NewGiftInfo(blackCardGift, giftUpgradeInfo.BlackCardGiftSmallIconURL),
			}
			blackCardGiftLevelMap[blackCardGift.GiftID] = giftUpgradeInfo.Level
		}
	}
	return nil
}

func (resp *metaResp) buildUpgradeGifts(giftElems []*metaGiftsElem, allShowGifts []gift.Gift) error {
	showGiftsMap := make(map[int64]*gift.Gift)
	for _, g := range allShowGifts {
		showGiftsMap[g.GiftID] = &g
	}
	upgradeGiftIDs := make([]int64, 0, len(giftElems))
	baseGifts := make([]usermeta.BaseGift, 0, len(giftElems))
	for _, item := range giftElems {
		for _, g := range item.Data {
			if g.IsUpgradeBaseGift() {
				upgradeGiftIDs = append(upgradeGiftIDs, g.GiftID)
				baseGifts = append(baseGifts, usermeta.BaseGift{
					GiftID:     g.GiftID,
					ToggleTime: g.ToggleTime,
				})
			}
		}
	}
	if len(baseGifts) == 0 {
		return nil
	}

	upgradeGiftsMap, err := livegiftupgrade.FindUpgradeGiftsMap(upgradeGiftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(upgradeGiftsMap) == 0 {
		return nil
	}

	recordsMap, err := livegiftupgrade.FindBaseGiftRecordsMap(resp.userID, baseGifts)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	userGiftUpgradeMap, err := usermeta.FindGiftUpgradeMaps(resp.userID, baseGifts)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	for _, item := range giftElems {
		for _, elemGift := range item.Data {
			if elemGift.IsUpgradeBaseGift() {
				elemGift.Upgrade = new(livegiftupgrade.UpgradeInfo)
				upgradeMap, ok := upgradeGiftsMap[elemGift.GiftID]
				if !ok {
					continue
				}
				baseGiftupgrade, ok := upgradeMap[elemGift.GiftID]
				if !ok {
					continue
				}

				// BuildGiftInfos
				giftsMap := make(map[int64]*gift.Gift)
				for giftID := range upgradeMap {
					if gift, ok := showGiftsMap[giftID]; ok {
						giftsMap[giftID] = gift
					}
				}
				elemGift.Upgrade.SeriesName = livegiftupgrade.BuildSeriesName(baseGiftupgrade)
				elemGift.Upgrade.Gifts = livegiftupgrade.BuildGiftInfos(upgradeMap, recordsMap[elemGift.GiftID], giftsMap)
				if !livegiftupgrade.IsAllCollected(upgradeMap, recordsMap[elemGift.GiftID]) {
					// BuildUpgradeNum
					elemGift.Upgrade.UpgradeNum = livegiftupgrade.BuildUpgradeNum(userGiftUpgradeMap[elemGift.GiftID], baseGiftupgrade)
					// BuildDiscountIcon
					elemGift.Upgrade.Discount = livegiftupgrade.BuildDiscountIcon(userGiftUpgradeMap[elemGift.GiftID], baseGiftupgrade)
				}
			}
		}
	}
	return nil
}

func (resp *metaResp) findMedal() error {
	if !resp.hasType(metaTypeMedal) || resp.userID == 0 {
		return nil
	}
	var err error
	resp.Medal, err = livemedal.FindOwnedMedal(resp.userID, resp.roomID, livemedal.FindOptions{OnlyMedal: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (resp *metaResp) findInteraction() error {
	if !resp.hasType(metaTypeInteraction) {
		return nil
	}
	result, err := interaction.Find(resp.roomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	switch result {
	case interaction.TypeInteractionVote:
		vote, err := livevote.FindOngoingVote(resp.roomID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if vote != nil {
			resp.Interaction = &Interaction{Vote: vote}
		}
	}
	return nil
}

func (resp *metaResp) findPK() error {
	if !resp.hasType(metaTypePK) {
		return nil
	}
	resp.PK = &PK{
		Detail: new(livepk.LivePK),
	}
	owner := resp.userID == resp.room.CreatorID
	if owner {
		resp.PK.Show = util.NewInt(1)
		resp.PK.IconURL = storage.ParseSchemeURL(config.Conf.Params.PK.EntryIcon)
	}

	// PK 匹配期
	pool, err := livepk.FindWaitingPKByRoomID(resp.roomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if pool != nil {
		// matching
		switch pool.Type {
		case livepk.PKTypeRandom:
			resp.PK.Detail.Status = livepk.PKRecordStatusMatching
			resp.PK.Detail.Duration = goutil.Milliseconds(livepk.PKMatchDuration)
			resp.PK.Detail.RemainDuration = pool.WaitingRemainDuration()
		case livepk.PKTypeInvitation:
			resp.PK.Detail = nil
			// 指定 PK 邀请信息仅主播端显示
			if !owner {
				return nil
			}
			invited := resp.roomID == pool.ToRoomID // 当前直播间是邀请方还是被邀请方, true: 被邀请方, false: 邀请方
			roomID := pool.ToRoomID
			if invited {
				roomID = pool.RoomID
			}
			r, err := room.FindOne(bson.M{"room_id": roomID}, &room.FindOptions{FindCreator: true})
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}
			if r == nil {
				return actionerrors.ErrCannotFindRoom
			}
			resp.PK.Invitation = &pkInvitation{
				MatchID:          pool.OID.Hex(),
				Invited:          goutil.BoolToInt(invited),
				Duration:         livepk.PKInvitationDuration.Milliseconds(),
				RemainDuration:   pool.WaitingRemainDuration(),
				FightingDuration: pool.Duration,
				CreateTime:       pool.CreateTime,
			}
			if invited {
				resp.PK.Invitation.FromRoom = &pkRoomInfo{
					RoomID:          r.RoomID,
					CreatorID:       r.CreatorID,
					CreatorUsername: r.CreatorUsername,
					CreatorIconURL:  r.CreatorIconURL,
					Statistics: &statistics{
						Score: r.Statistics.Score,
					},
				}
			} else {
				resp.PK.Invitation.ToRoom = &pkRoomInfo{
					RoomID:          r.RoomID,
					CreatorID:       r.CreatorID,
					CreatorUsername: r.CreatorUsername,
					CreatorIconURL:  r.CreatorIconURL,
				}
			}
		}
		return nil
	}

	// PK 进行期、惩罚期、普通连麦期对战详情
	resp.PK.Detail, err = livepk.FindCurrentPKRecord(resp.roomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if resp.PK.Detail == nil {
		// 不下发空的 pk 字段
		if resp.PK.Show == nil {
			resp.PK = nil
		}
		return nil
	}
	// 直播间详情
	_, err = resp.PK.Detail.Fighters[1].FindAndSetRoom()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	resp.PK.Detail.Fighters[0].Name = resp.room.Name
	resp.PK.Detail.Fighters[0].CreatorUsername = resp.room.CreatorUsername
	resp.PK.Detail.Fighters[0].CreatorIconURL = resp.room.CreatorIconURL
	// 粉丝榜
	if err := resp.PK.Detail.FindTopFans(3); err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (resp *metaResp) findMultiConnect() error {
	if !resp.hasType(metaTypeMultiConnect) {
		return nil
	}
	// 未配置开始时间或早于主播连线功能开始时间直接返回
	mcCfg, err := params.FindMultiConnect()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	now := goutil.TimeNow()

	// 判断多人连线是否开放
	if !mcCfg.IsOpen(now, resp.roomID) {
		return nil
	}

	resp.MultiConnect = &MultiConnect{}
	isRoomOwner := resp.userID == resp.room.CreatorID
	if isRoomOwner {
		// 仅主播在自己直播间时显示入口
		resp.MultiConnect.Show = util.NewInt(1)
		resp.MultiConnect.IconURL = mcCfg.EntryIcon
	}

	// 查找当前主播连线的成员列表
	members, err := livemulticonnect.FindOngoingMembersByRoomID(resp.roomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(members) == 0 {
		return nil
	}
	group, err := livemulticonnect.FindOngoingGroup(members[0].GroupID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if group == nil {
		return actionerrors.ErrMultiConnectGroupNotFound
	}
	micOffRoomIDs, err := group.MicOffRoomIDs()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	resp.MultiConnect.Detail = &multiConnectDetail{}
	memberInfos, err := livemulticonnect.BuildMemberInfos(members, resp.roomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, logger.Fields{"group_id": members[0].GroupID})
	}

	// 获取主播连线中的房间信息
	roomIDs := goutil.SliceMap(memberInfos, func(info *livemulticonnect.GroupMemberInfo) int64 {
		return info.RoomID
	})
	rooms, err := room.List(bson.M{"room_id": bson.M{"$in": roomIDs}}, nil, &room.FindOptions{FindCreator: true, FindFans: true, ListenerID: resp.userID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(roomIDs) != len(rooms) {
		return actionerrors.ErrCannotFindRoom
	}

	roomMap := util.ToMap(rooms, func(r *room.Room) int64 {
		return r.RoomID
	})
	// 获取连线中被本房间静音的成员
	for _, member := range members {
		if member.RoomID == resp.roomID {
			resp.MultiConnect.Detail.MuteRoomIDs, err = member.MutedRoomIDs()
			if err != nil {
				logger.WithFields(logger.Fields{"room_id": resp.roomID}).Error("获取主播连线静音成员列表失败")
				// PASS
			}
			break
		}
	}

	var isGroupOwner bool // 当前房间是否为主麦
	for _, member := range memberInfos {
		r := roomMap[member.RoomID]
		if member.Role == livemulticonnect.MemberRoleOwner && member.RoomID == resp.roomID {
			isGroupOwner = true
		}
		memberInfo := multiConnectMemberInfo{
			Index: member.Index,
			Role:  member.Role,
			Score: member.Score,
			Room: multiConnectRoomInfo{
				RoomID:          member.RoomID,
				CreatorID:       r.CreatorID,
				CreatorUsername: r.CreatorUsername,
				CreatorIconURL:  r.CreatorIconURL,
				Status: multiConnectRoomStatus{
					Attention: r.Status.Attention,
				},
			},
		}
		resp.MultiConnect.Detail.Members = append(resp.MultiConnect.Detail.Members, memberInfo)
	}

	// 成员列表第一个是本房间
	resp.MultiConnect.Detail.GroupID = memberInfos[0].GroupID
	resp.MultiConnect.Detail.Status = livemulticonnect.MemberStatusOngoing
	resp.MultiConnect.Detail.Duration = now.UnixMilli() - memberInfos[0].StartTime
	resp.MultiConnect.Detail.MicOffRoomIDs = micOffRoomIDs
	if isRoomOwner {
		// 只对主播下发用户榜单相关配置
		if isGroupOwner {
			// 在白名单中的主麦可以调整榜单开关
			resp.MultiConnect.Detail.Config.UserRank.Granted = livemulticonnect.GetUserRankGranted(mcCfg, resp.roomID)
		}
		resp.MultiConnect.Detail.Config.UserRank.Enable = livemulticonnect.GetUserRankEnabled(group.ID)
	}
	return nil
}

func (resp *metaResp) findEvents() error {
	if !resp.hasType(metaTypeEvents) {
		return nil
	}

	allEvents, err := liverecommendedelements.FindAllEvents()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	resp.Events, err = resp.findAllWorkingEvents(allEvents)
	if err != nil {
		return err
	}
	return nil
}

func (resp *metaResp) findAllWorkingEvents(allEvents []liverecommendedelements.Event) ([]liverecommendedelements.Event, error) {
	workingEvents := make([]liverecommendedelements.Event, 0, len(allEvents))
	isOldApp := resp.equip.IsAppOlderThan("6.2.0", "6.2.0") // 检查版本号决定返回新或老 cover
	for _, e := range allEvents {
		if !isOldApp {
			e.ShowClose = false // App 新版本、Web 端，去掉关闭按钮
		}
		if e.ExtendedFields == "" {
			workingEvents = append(workingEvents, e)
			continue
		}
		var extendedFields liverecommendedelements.EventExtendedFields
		if err := json.Unmarshal([]byte(e.ExtendedFields), &extendedFields); err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		conf := roomShowConfig{
			AllRoomShow: extendedFields.IsAllRoomShow(),
			CatalogIDs:  extendedFields.CatalogIDs,
			TagIDs:      extendedFields.TagIDs,
			RoomIDs:     extendedFields.RoomIDs,
		}
		if isOldApp && extendedFields.OldCover != "" {
			e.Cover = storage.ParseSchemeURL(extendedFields.OldCover)
		}
		if conf.shouldShow(resp) {
			workingEvents = append(workingEvents, e)
			continue
		}
	}
	return workingEvents, nil
}

type roomShowConfig struct {
	AllRoomShow       bool
	ApplicationType   int
	CatalogIDs        []int64
	TagIDs            []int64
	RoomIDs           []int64
	RoomApplicationID int64
}

func (config roomShowConfig) shouldShow(meta *metaResp) bool {
	if config.AllRoomShow {
		return true
	}

	room := meta.room
	isInRooms := func() (bool, error) {
		if config.RoomApplicationID == 0 {
			return goutil.HasElem(config.RoomIDs, room.RoomID), nil
		}

		ok, err := application.ElementExists(config.RoomApplicationID, room.RoomID)
		if err != nil {
			return false, err
		}
		return ok, nil
	}

	isInTags := func() (bool, error) {
		if meta.allShowTags == nil {
			allShowTags, err := tag.AllShowLiveTags()
			if err != nil {
				return false, err
			}
			meta.allShowTags = allShowTags
		}
		if meta.allShowTags != nil && meta.allShowTags.IncludeTagIDs(config.TagIDs, room.TagIDs) {
			return true, nil
		}

		return false, nil
	}

	isInCatalogs := func() (bool, error) {
		if room.CatalogID == 0 {
			return false, nil
		}
		if meta.subCatalogMap == nil {
			// 查询所有可见的一级分区，一级分区的 SubCatalogs 会有二级分区的信息
			catalogs, err := catalog.LiveCatalogs(false)
			if err != nil {
				return false, err
			}
			subToTopMap := make(map[int64]int64, len(catalogs)*7)
			for _, c := range catalogs {
				for _, subCatalog := range c.SubCatalogs {
					subToTopMap[subCatalog.ID] = c.ID
				}
			}
			meta.subCatalogMap = subToTopMap
		}
		// room.CatalogID 只可能为二级分区
		catalogID := room.CatalogID
		parentCatalogID, ok := meta.subCatalogMap[catalogID]
		if !ok || parentCatalogID == 0 {
			return false, nil
		}
		return len(util.IntersectionInt64(config.CatalogIDs, []int64{catalogID, parentCatalogID})) > 0, nil
	}

	// 查询主播是否在指定的房间、标签、分区名单中
	checkInList := func() (bool, error) {
		// 分区、标签、直播间名单之间取并集
		funcList := []func() (bool, error){isInRooms, isInTags, isInCatalogs}
		for _, f := range funcList {
			ok, err := f()
			if err != nil {
				return false, err
			}
			if ok {
				return true, nil
			}
		}

		return false, nil
	}

	inList, err := checkInList()
	// 如果查询失败，则不展示小窗
	if err != nil {
		logger.Error(err)
		return false
	}

	if config.ApplicationType == liverecommendedelements.ApplicationBlockList {
		return !inList
	}
	return inList
}

func (resp *metaResp) findHourRank() (err error) {
	if !resp.hasType(metaTypeHourRank) {
		return nil
	}
	resp.HourRank = new(hourRank)
	resp.HourRank.Rank, resp.HourRank.RankUp, err = usersrank.HourRank(resp.room.CreatorID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (resp *metaResp) findPopups() error {
	if !resp.hasType(metaTypePopups) {
		return nil
	}
	// WORKAROUND: 大流量主播的小窗默认折叠
	isSpecialCreator := resp.room.IsSpecialCreator()
	// WORKAROUND: 安卓 < 5.6.5 或 iOS < 4.7.7 的版本, 返回的是旧格式, 并且大流量主播默认不返回小窗
	isOldApp := resp.equip.IsOldApp(goutil.AppVersions{
		IOS:     "4.7.7",
		Android: "5.6.5",
	})
	if isOldApp && isSpecialCreator {
		return nil
	}

	showPopups, err := resp.findShowPopupsByRoom()
	if err != nil {
		return err
	}
	popups := make([]*liverecommendedelements.PopupRespItem, 0, len(showPopups))
	for _, p := range showPopups {
		if p.URL == "" && p.Cover == "" {
			return actionerrors.ErrCannotFindResource
		}
		popupItem, err := liverecommendedelements.NewPopupItem(p)
		if err != nil {
			logger.Errorf("new popup item error, err: %v", err)
			continue
		}
		if popupItem.MiniURL == "" || (popupItem.OpenURL == "" && popupItem.FullURL == "") {
			logger.Errorf("find popup error: parse popup url failed, popup url: %s, popup cover: %s", p.URL, p.Cover)
			continue
		}
		popups = append(popups, popupItem)
	}

	if isOldApp {
		resp.Popups = popups
	} else {
		newPopups := new(NewPopups)
		newPopups.Fold = isSpecialCreator
		newPopups.Data = popups
		resp.Popups = newPopups
	}
	return nil
}

func (resp *metaResp) findShowPopupsByRoom() ([]liverecommendedelements.Popup, error) {
	popups, err := liverecommendedelements.FindAllPopups()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	showPopups := make([]liverecommendedelements.Popup, 0, len(popups))
	// TODO: tag 和 catalog 在被隐藏的时候，不展示
	for _, p := range popups {
		if p.ExtendedFields == "" {
			// 为了兼容之前的配置，ExtendedFields 为空时表示小窗可以在所有直播间显示
			showPopups = append(showPopups, p)
			continue
		}
		var config liverecommendedelements.PopupShowConfig
		if err := json.Unmarshal([]byte(p.ExtendedFields), &config); err != nil {
			logger.Error(err)
			// PASS
			continue
		}
		c := roomShowConfig{
			AllRoomShow:       config.AllRoomShow,
			ApplicationType:   config.ApplicationType,
			CatalogIDs:        config.CatalogIDs,
			TagIDs:            config.TagIDs,
			RoomIDs:           config.RoomIDs,
			RoomApplicationID: config.RoomApplicationID,
		}
		if c.shouldShow(resp) {
			showPopups = append(showPopups, p)
			continue
		}
	}
	return showPopups, nil
}

func (resp *metaResp) findGashapon() (err error) {
	if !resp.hasType(metaTypeGashapon) {
		return nil
	}
	if !resp.room.IsOpen() {
		// 直播间未开播，不返回超能魔方入口
		return nil
	}
	// WORKAROUND: Android < 6.3.5 和 iOS < 6.3.5 的版本不返回超能魔方
	if resp.equip.IsOldApp(goutil.AppVersions{IOS: "6.3.5", Android: "6.3.5"}) {
		return nil
	}

	config, err := params.FindGashapon()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !resp.equip.FromApp && config.WebOldVersion {
		return nil
	}
	if !config.IsShowInInteractive() {
		return nil
	}

	buff, err := gift.FindGashaponBuff(resp.roomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	resp.Gashapon = &gashapon{
		DefaultIconURL: config.ImageURL,
		OpenURL:        config.OpenURL,
		LabelIconURL:   config.InteractionLabelIconURL,
		Buff:           buff,
	}
	return nil
}

func (resp *metaResp) findGiftWall() error {
	if !resp.hasType(metaTypeGiftWall) {
		return nil
	}

	var err error
	resp.GiftWall, err = giftwall.FindActivatedDetail(resp.roomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (resp *metaResp) findRedPacket() error {
	if !resp.hasType(metaTypeRedPacket) {
		return nil
	}

	param, err := params.FindRedPacket()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// NOTICE: 如在黑名单中则不下发红包入口
	if param.IsRoomDisabled(resp.roomID) {
		return nil
	}
	if param.Name == "" {
		return nil
	}
	// 兼容旧版本
	if resp.equip.IsOldApp(goutil.AppVersions{IOS: "6.2.5", Android: "6.2.5"}) ||
		(!resp.equip.FromApp && param.WebOldVersion) {
		resp.RedPacket = &RedPacket{
			Name:          param.Name,
			Price:         param.Price,
			IconURL:       param.IconURL,
			ImageURL:      param.ImageURL,
			BigIconURL:    param.BigIconURL,
			CornerIconURL: param.CornerIconURL,
			IconActiveURL: param.IconActiveURL,
			Intro:         param.Intro,
			IntroIconURL:  param.IntroIconURL,
			IntroOpenURL:  param.IntroOpenURL,
			Position:      param.Position,
			List:          []*redpacket.Elem{},
		}
	} else {
		resp.RedPacket = &RedPacket{
			Name:       param.Name,
			IconURL:    param.IconURL,
			ImageURL:   param.ImageURL,
			BigIconURL: param.BigIconURL,
			List:       []*redpacket.Elem{},
		}
	}

	// 获取直播间内礼物红包列表
	err = resp.findRedPacketList()
	if err != nil {
		return err
	}
	return nil
}

func (resp *metaResp) findRedPacketList() error {
	liveRedPacketList, err := liveredpacket.ListByRoomID(resp.roomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(liveRedPacketList) == 0 {
		return nil
	}

	// 礼物红包 ID
	redPacketOIDs := make([]primitive.ObjectID, 0, len(liveRedPacketList))
	for _, liveRedPacket := range liveRedPacketList {
		redPacketOIDs = append(redPacketOIDs, liveRedPacket.OID)
	}

	var grabbedRedPacketOIDs []primitive.ObjectID
	if resp.userID != 0 {
		// 若用户已登录，查询用户抢过的红包 IDs
		grabbedRedPacketOIDs, err = liveredpacket.FindUserGrabbedRedPacketOIDs(resp.userID, redPacketOIDs)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if len(grabbedRedPacketOIDs) == len(redPacketOIDs) {
			// 若该直播间下的红包该用户都抢过，直接 return
			return nil
		}
		// 用户未抢过的红包列表
		noGrabbedLiveRedPacketList := make([]*liveredpacket.LiveRedPacket, 0, len(liveRedPacketList)-len(grabbedRedPacketOIDs))
		for _, liveRedPacket := range liveRedPacketList {
			if !goutil.HasElem(grabbedRedPacketOIDs, liveRedPacket.OID) {
				// 排除用户抢过的红包
				noGrabbedLiveRedPacketList = append(noGrabbedLiveRedPacketList, liveRedPacket)
			}
		}
		liveRedPacketList = noGrabbedLiveRedPacketList
	}

	var (
		// 发红包用户 ID
		senderIDs = make([]int64, 0, len(liveRedPacketList))
		// 礼物红包商品 ID
		goodsIDs = make([]int64, 0, len(liveRedPacketList))
		// 外观中心 ID
		appearanceIDs = make([]int64, 0, len(liveRedPacketList))
	)
	for _, liveRedPacket := range liveRedPacketList {
		senderIDs = append(senderIDs, liveRedPacket.UserID)
		goodsIDs = append(goodsIDs, liveRedPacket.GoodsID)
		appearanceIDs = append(appearanceIDs, liveRedPacket.AppearanceID)
	}
	goodsIDs = util.Uniq(goodsIDs)
	// 礼物红包商品列表
	liveGoodsList, err := livegoods.ListByIDs(goodsIDs, livegoods.GoodsTypeRedPacket)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(liveGoodsList) == 0 {
		// 礼物红包商品都不存在，则记录日志并直接 return
		logger.WithField("goods_ids", goodsIDs).Error("礼物红包商品都不存在")
		return nil
	}
	if len(liveGoodsList) != len(goodsIDs) {
		// 礼物红包商品部分不存在，则记录日志
		logger.WithField("goods_ids", goodsIDs).Error("部分礼物红包商品不存在")
	}
	liveGoodsMap := make(map[int64]livegoods.LiveGoods, len(liveGoodsList)) // key: goods_id
	liveGoodsMoreMap := make(map[int64]*livegoods.More, len(liveGoodsList)) // key: goods_id
	// 礼物 ID 数组
	giftIDs := make([]int64, 0, len(liveGoodsList)*livegoods.MaxCountRedPacketGiftItem)
	for _, liveGoods := range liveGoodsList {
		liveGoodsMap[liveGoods.ID] = liveGoods
		more, err := liveGoods.UnmarshalMore()
		if err != nil {
			logger.WithField("goods_id", liveGoods.ID).Error("礼物红包商品的配置 More 信息解析错误")
			continue
		}
		if !more.IsValidRedPacket() {
			logger.WithField("goods_id", liveGoods.ID).Error("礼物红包商品的配置信息不存在")
			continue
		}
		liveGoodsMoreMap[liveGoods.ID] = more

		// 获取商品配置的礼物 ID
		for _, g := range more.RedPacket.Gifts {
			giftIDs = append(giftIDs, g.ID)
		}
	}
	// 获取礼物信息 map
	giftIDs = util.Uniq(giftIDs)
	giftMap, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(giftMap) != len(giftIDs) {
		logger.WithField("gift_ids", giftIDs).Error("礼物红包商品配置的礼物部分不存在")
	}
	// NOTICE: 使用的外观以发红包时的外观为准，而不是当前用户正在使用的外观
	appearances, err := appearance.Find(bson.M{"id": bson.M{"$in": util.Uniq(appearanceIDs)}}, nil)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	appearanceMap := goutil.ToMap(appearances, "ID").(map[int64]*appearance.Appearance)

	redPacketGiftMap := make(map[int64][]*redpacket.Gift, len(liveGoodsList)) // key: goods_id
	for liveGoodsID, liveGoodsMore := range liveGoodsMoreMap {
		redPacketGiftList := make([]*redpacket.Gift, 0, len(liveGoodsMore.RedPacket.Gifts))
		for _, goodsRedPacketGift := range liveGoodsMore.RedPacket.Gifts {
			g, ok := giftMap[goodsRedPacketGift.ID]
			if !ok {
				logger.WithFields(logger.Fields{"goods_id": liveGoodsID, "gift_id": goodsRedPacketGift.ID}).Error("礼物红包商品配置的礼物不存在")
				continue
			}
			redPacketGiftList = redpacket.AppendGifts(redPacketGiftList, goodsRedPacketGift, g)

			// 计算红包礼物的总价和总数
			liveGoodsMore.RedPacket.GiftTotalPrice += g.Price * goodsRedPacketGift.Num
			liveGoodsMore.RedPacket.GiftTotalNum += goodsRedPacketGift.Num
		}
		redPacketGiftMap[liveGoodsID] = redPacketGiftList
	}

	// 查询发红包用户的信息
	senderMap, err := mowangskuser.FindSimpleMap(util.Uniq(senderIDs))
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	resp.RedPacket.List = make([]*redpacket.Elem, 0, len(liveRedPacketList))
	nowUnixMilli := util.TimeToUnixMilli(goutil.TimeNow())
	for _, liveRedPacket := range liveRedPacketList {
		redPacketID := liveRedPacket.OID.Hex()
		liveGoods, ok := liveGoodsMap[liveRedPacket.GoodsID]
		if !ok {
			logger.WithFields(logger.Fields{"red_packet_id": redPacketID, "goods_id": liveRedPacket.GoodsID}).
				Error("礼物红包商品不存在")
			continue
		}
		liveGoodsMore, ok := liveGoodsMoreMap[liveRedPacket.GoodsID]
		if !ok {
			logger.WithFields(logger.Fields{"red_packet_id": redPacketID, "goods_id": liveRedPacket.GoodsID}).
				Error("礼物红包商品配置信息 More 不存在")
			continue
		}
		redPacketGiftList, ok := redPacketGiftMap[liveRedPacket.GoodsID]
		if !ok {
			logger.WithFields(logger.Fields{"red_packet_id": redPacketID, "goods_id": liveRedPacket.GoodsID}).
				Error("礼物红包商品配置的礼物信息不存在")
			continue
		}
		sender, ok := senderMap[liveRedPacket.UserID]
		if !ok {
			logger.WithFields(logger.Fields{"red_packet_id": redPacketID, "user_id": liveRedPacket.UserID}).
				Error("发红包用户的信息不存在")
			continue
		}
		redPacketElem := &redpacket.Elem{
			RedPacketID:    redPacketID,
			Type:           liveGoodsMore.RedPacket.Type,
			CornerIconURL:  liveGoodsMore.RedPacket.CornerIconURL(),
			Price:          liveGoods.Price,
			GiftTotalPrice: liveGoodsMore.RedPacket.GiftTotalPrice,
			Sender:         sender,
			GiftTotalNum:   liveGoodsMore.RedPacket.GiftTotalNum,
			Gifts:          redPacketGiftList,
		}
		// 正常发红包的时候如用户没有外观，会存入该红包所属红包商品默认的外观 ID
		if liveRedPacket.AppearanceID != 0 {
			appearanceItem, ok := appearanceMap[liveRedPacket.AppearanceID]
			if !ok {
				logger.WithFields(logger.Fields{
					"red_packet_id": redPacketID,
					"appearance_id": liveRedPacket.AppearanceID,
					"user_id":       liveRedPacket.UserID,
				}).Error("未查询到该外观")
				continue
			}
			redPacketElem.SkinURL = storage.ParseSchemeURL(appearanceItem.Resource)
		} else {
			// TODO: 兼容上线前发送的红包没有设置外观 ID 的情况，上线一天后可删除兼容代码
			redPacketElem.SkinURL = storage.ParseSchemeURL(liveGoodsMore.RedPacket.Skin)
		}

		if liveRedPacket.WaitDuration != 0 {
			// 红包剩余倒计时（单位：毫秒）
			redPacketElem.RemainDuration = liveRedPacket.StartGrabTime*1000 - nowUnixMilli
			if redPacketElem.RemainDuration < 0 {
				// 红包剩余倒计时有低机率会小于 0，这时候赋值为 0
				redPacketElem.RemainDuration = 0
			}
		}
		resp.RedPacket.List = append(resp.RedPacket.List, redPacketElem)
	}
	return nil
}

func (resp *metaResp) findShops() error {
	if !resp.hasType(metaTypeRedeemShop) {
		return nil
	}
	err := resp.setRedeemShop()
	if err != nil {
		return err
	}
	return resp.setPrivilegeShop()
}

func (resp *metaResp) setRedeemShop() error {
	// WORKAROUND: iOS < 6.0.0 客户端上显示万事屋入口会有问题，因此不下发入口
	if resp.equip.OS == goutil.IOS && resp.equip.IsOldApp(goutil.AppVersions{IOS: "6.0.0"}) {
		return nil
	}

	param, err := params.FindRedeemShop()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !param.IsShow() {
		return nil
	}
	shopURL, err := param.GetShopURL(resp.roomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	resp.RedeemShop = &RedeemShop{
		Name:       param.Name,
		IconURL:    param.IconURL,
		WebIconURL: param.WebIconURL,
		ShopURL:    shopURL,
	}
	return nil
}

func (resp *metaResp) setPrivilegeShop() error {
	// WORKAROUND: iOS < 6.1.3 客户端上显示星享馆入口会有问题，因此不下发入口
	if resp.equip.OS == goutil.IOS && resp.equip.IsOldApp(goutil.AppVersions{IOS: "6.1.3"}) {
		return nil
	}
	// 主播侧不显示星享馆入口
	if resp.userID == resp.room.CreatorID {
		return nil
	}
	param, err := params.FindPrivilegeShop()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !param.IsShow() {
		return nil
	}
	shopURL, err := param.GetShopURL(resp.roomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	resp.PrivilegeShop = &RedeemShop{
		Name:       param.Name,
		IconURL:    param.IconURL,
		WebIconURL: param.WebIconURL,
		ShopURL:    shopURL,
	}
	return nil
}

func (resp *metaResp) findPia() {
	if !resp.hasType(metaTypePia) {
		return
	}
	// WORKAROUND: 仅 Web 端下发该入口
	if resp.equip.OS != goutil.Web {
		return
	}
	// 仅主播端下发该入口
	if resp.room.CreatorID != resp.userID {
		return
	}

	param, err := params.FindPia()
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	if !param.IsShow() {
		return
	}

	allow, err := livetagcontrollist.IsAllowRoomAddTag(tag.TagListenDrama, resp.room.RoomID)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	if !allow {
		return
	}
	resp.Pia = &Pia{
		Name:   param.Name,
		Status: piaStatusNotStarted,
	}
	if !resp.room.IsOpen() || !resp.room.ContainsTag(tag.TagListenDrama) {
		return
	}
	record, err := liveroomtagrecord.FindLastRecord(tag.TagListenDrama, resp.room.RoomID)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	resp.Pia.Status = piaStatusOngoing
	// NOTICE: 若 pia 戏开始时间小于直播开播时间，则按照直播开播时间计算 pia 戏时长
	switch {
	case record != nil && record.Operation == liveroomtagrecord.OperationAdd && time.Unix(record.CreateTime, 0).UnixMilli() >= resp.room.Status.OpenTime:
		resp.Pia.Duration = goutil.TimeNow().Sub(time.Unix(record.CreateTime, 0)).Milliseconds()
	default:
		resp.Pia.Duration = goutil.TimeNow().Sub(util.UnixMilliToTime(resp.room.Status.OpenTime)).Milliseconds()
	}
}

func (resp *metaResp) findMultiCombo() {
	if !resp.hasType(metaTypeMultiCombo) {
		return
	}
	resp.MultiCombo = &multiCombo{}
	if resp.room.CreatorID == resp.userID {
		// 主播端不需要展示一起送快捷按钮
		return
	}
	err := resp.findMultiComboParams()
	if err != nil {
		logger.WithField("room_id", resp.roomID).Error(err)
		// PASS
		return
	}
	if !resp.multiComboParams.EnableSend(resp.roomID) {
		return
	}
	if !resp.multiComboParams.EnableShowShortcuts() {
		return
	}
	// 一起送连击进度主要展示在快捷按钮上，快捷按钮需要展示时才要查询一起送连击信息
	resp.MultiCombo = &multiCombo{
		Status: 1,
		Combos: findMultiCombos(resp.roomID),
	}
}

func (resp *metaResp) findMultiComboParams() error {
	if resp.multiComboParams == nil {
		comboParam, err := params.FindMultiCombo()
		if err != nil {
			return err
		}
		resp.multiComboParams = &comboParam
	}
	return nil
}

func findMultiCombos(roomID int64) []*multiComboGift {
	gifts, err := gift.FindShowingMultiComboGifts()
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	if len(gifts) == 0 {
		return nil
	}
	cmds := make([]*redis.SliceCmd, 0, len(gifts))
	pipe := service.Redis.TxPipeline()
	for i := range gifts {
		cmds = append(cmds, pipe.HMGet(
			keys.KeyRoomGiftMultiCombo2.Format(roomID, gifts[i].GiftID),
			keys.FieldComboID0.Format(),
			keys.FieldComboGiftNum0.Format(),
			keys.FieldComboLastTimeMilli0.Format(),
		))
	}
	_, err = pipe.Exec()
	if err != nil {
		logger.WithField("room_id", roomID).Error(err)
		return nil
	}
	combos := make([]*multiComboGift, 0, len(gifts))
	for i := range cmds {
		res := cmds[i].Val()
		if res[0] == nil || res[1] == nil || res[2] == nil {
			continue
		}
		comboID, ok := res[0].(string)
		if !ok {
			continue
		}
		giftNumStr, ok := res[1].(string)
		if !ok {
			continue
		}
		giftNum, err := strconv.Atoi(giftNumStr)
		if err != nil {
			logger.Error(err)
			continue
		}
		lastTimeStr, ok := res[2].(string)
		if !ok {
			continue
		}
		lastTimeMilliUnix, err := strconv.ParseInt(lastTimeStr, 10, 64)
		if err != nil {
			continue
		}
		g := gifts[i]
		now := goutil.TimeNow()
		lastTime := goutil.TimeUnixMilli(lastTimeMilliUnix).ToTime()
		remainTime := lastTime.Add(gift.ComboRemainDuration(int64(giftNum) * g.Price))
		if now.Before(remainTime) {
			comboLevel := g.ComboLevel(g.Price * int64(giftNum))
			combos = append(combos, &multiComboGift{
				Gift: gift.NotifyGift{
					GiftID:  g.GiftID,
					Name:    g.Name,
					IconURL: g.Icon,
					Price:   g.Price,
				},
				Combo: livegifts.Combo{
					ID:          comboID,
					TotalNum:    giftNum,
					AchievedNum: &comboLevel.AchievedNum,
					TargetNum:   comboLevel.TargetNum,
					RemainTime:  remainTime.Sub(now).Milliseconds(),
				},
			})
		}
	}
	return combos
}

func (resp *metaResp) findShortcutGift() {
	if !resp.hasType(metaTypeShortcutGift) {
		return
	}
	shortcut, err := livecustom.FindRoomShowShortcut(resp.roomID)
	if err != nil {
		logger.WithField("room_id", resp.roomID).Error(err)
		// PASS
		return
	}
	if shortcut == nil {
		return
	}
	g, err := gift.FindShowingGiftByGiftID(shortcut.CustomID)
	if err != nil {
		logger.WithField("gift_id", shortcut.CustomID).Error(err)
		// PASS
		return
	}
	if g == nil {
		// 快捷送礼支持配置未上线的礼物，查询不到礼物时记录 WARN 日志
		logger.WithField("gift_id", shortcut.CustomID).Warn("快捷送礼礼物不存在")
		return
	}
	resp.ShortcutGift = g.NewShortcutGift()
}

func (resp *metaResp) findMembers() {
	if !resp.hasType(metaTypeMembers) {
		return
	}
	opt := &livemembers.FindMemberOptions{
		FindUserInfo: true,
	}
	if resp.userID == 0 {
		// 游客不需要返回 mute 列表
		opt.OnlyAdmin = true
	}
	admin, mute, err := livemembers.ListMembers(resp.room.OID, opt)
	if err != nil {
		logger.WithField("room_id", resp.roomID).Error(err)
		// PASS
	}
	resp.Members = &metaMembers{
		Admin: make([]metaMemberElem, 0, len(admin)),
		Mute:  make([]metaMemberElem, 0, len(mute)),
	}
	for _, a := range admin {
		resp.Members.Admin = append(resp.Members.Admin, newMetaMemberElem(a))
	}
	for _, m := range mute {
		resp.Members.Mute = append(resp.Members.Mute, newMetaMemberElem(m))
	}
}

func newMetaMemberElem(member *livemembers.Member) metaMemberElem {
	elem := metaMemberElem{
		RoomID:   member.RoomID,
		UserID:   member.UserID,
		Username: member.Username,
		IconURL:  member.IconURL,
	}
	if member.ExpireAt != nil {
		elem.ExpireAt = member.ExpireAt.Unix()
	}
	return elem
}

func (resp *metaResp) findConfig() {
	if !resp.hasType(metaTypeConfig) {
		return
	}
	if resp.room.Config != nil && resp.room.Config.AllowHideGiftEffect {
		resp.Config = &metaConfig{
			AllowHideGiftEffect: true,
		}
	}
}

func (resp *metaResp) findLuckyBagEntry() {
	if !resp.hasType(metaTypeLuckyBag) {
		return
	}

	// 不是当前房间的主播不返回福袋入口信息
	// 发起福袋只有网页能发起，客户端不返回入口信息
	if resp.room.CreatorID != resp.userID || resp.equip.FromApp {
		return
	}

	config, err := params.FindLuckyBag()
	if err != nil {
		logger.Error(err)
		return
	}
	if !config.IsOpen() {
		return
	}

	// 判断直播间是否在福袋黑名单中
	exists, err := application.IsRoomLuckyBagInitiateBlocked(resp.roomID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if exists {
		return
	}

	resp.LuckyBagEntry = &luckyBagEntry{
		Name: config.Name,
	}
}

func (resp *metaResp) findLuckyBag() {
	if !resp.hasType(metaTypeLuckyBag) {
		return
	}

	// web 用户侧不下发福袋信息，客户端下发
	if resp.room.CreatorID != resp.userID && !resp.equip.FromApp {
		return
	}

	record, err := luckybag.FindLatestInitiateRecord(resp.roomID)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	if record == nil {
		return
	}

	config, err := params.FindLuckyBag()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	resp.LuckyBag = record.NewRoomMetaMessage(resp.userID, config.ImageURL, config.NewImageURL, config.BigImageURL)
}

func (resp *metaResp) findWishListEntry() {
	if !resp.hasType(metaTypeWishList) {
		return
	}

	// 不是当前房间的主播不返回心愿单入口信息
	// 发起心愿单只有网页能发起，客户端暂时一期不做
	if resp.room.CreatorID != resp.userID || resp.equip.FromApp {
		return
	}

	config, err := params.FindWishList()
	if err != nil {
		logger.Error(err)
		return
	}
	if !config.IsOpen() {
		return
	}

	resp.WishListEntry = &wishListEntry{
		Name: config.Name,
	}
}

func (resp *metaResp) findLuckyBox() {
	if !resp.hasType(metaTypeLuckyBox) {
		return
	}
	param, err := params.FindLuckyBox()
	if err != nil {
		return
	}
	if param.Name == "" {
		return
	}
	if !param.IsOpen() {
		// 未开始宝盒活动时，不返回宝盒入口
		return
	}
	if !param.IsShowInInteractive() {
		return
	}
	resp.LuckyBox = &luckyBox{
		Name:         param.Name,
		ImageURL:     param.ImageURL,
		LabelIconURL: param.InteractionLabelIconURL,
	}
}

func (resp *metaResp) findInteractiveGifts() error {
	if len(resp.Gifts) == 0 || len(resp.Gifts[0].Data) == 0 {
		return nil
	}
	specialGifts := make([]*metaGift, 0, gift.SpecialGiftNum)
	// 依次处理各类交互式礼物
	interactiveGiftFinders := []func() (*metaGift, error){
		resp.findGiftRedPacket,
		resp.findGiftLuckyBox,
		resp.findGiftGashapon,
	}
	// 遍历查找各类交互式礼物
	for _, finder := range interactiveGiftFinders {
		gift, err := finder()
		if err != nil {
			return err
		}
		if gift != nil {
			specialGifts = append(specialGifts, gift)
		}
	}
	// 排序后按位置下标插入
	sort.Slice(specialGifts, func(i, j int) bool {
		return specialGifts[i].position < specialGifts[j].position
	})
	for _, v := range specialGifts {
		resp.Gifts[0].Data = slices.Insert(resp.Gifts[0].Data, v.position, v)
	}
	return nil
}

func (resp *metaResp) findGiftRedPacket() (*metaGift, error) {
	// WORKAROUND: Android < 6.2.5 和 iOS < 6.2.5 的版本不在礼物列表返回红包玩法
	if resp.equip.IsOldApp(goutil.AppVersions{IOS: "6.2.5", Android: "6.2.5"}) {
		return nil, nil
	}
	redPacketParam, err := params.FindRedPacket()
	if err != nil {
		return nil, err
	}
	// Web 端兼容：老版本不在礼物列表下发红包跟盲盒等玩法的礼物
	if !resp.equip.FromApp && redPacketParam.WebOldVersion {
		return nil, nil
	}
	// NOTICE: 如在黑名单中则不下发红包礼物
	if redPacketParam.Name == "" || redPacketParam.IsRoomDisabled(resp.roomID) {
		return nil, nil
	}
	redPacketGift := &metaGift{
		Gift: gift.Gift{
			Type:         gift.TypeSpecial,
			Name:         redPacketParam.Name,
			Icon:         redPacketParam.IconURL,
			IconActive:   redPacketParam.IconActiveURL,
			Intro:        redPacketParam.Intro,
			IntroIcon:    redPacketParam.IntroIconURL,
			IntroOpenURL: redPacketParam.IntroOpenURL,
			Price:        redPacketParam.Price,
			LabelIcon:    redPacketParam.CornerIconURL,
		},
		SpecialType: gift.SpecialTypeRedPacket,
		PriceType:   redPacketParam.PriceType,
		position:    redPacketParam.Position,
	}
	return redPacketGift, nil
}

func (resp *metaResp) findGiftLuckyBox() (*metaGift, error) {
	// WORKAROUND: Android < 6.2.5 和 iOS < 6.2.5 的版本不在礼物列表返回宝盒玩法
	if resp.equip.IsOldApp(goutil.AppVersions{IOS: "6.2.5", Android: "6.2.5"}) {
		return nil, nil
	}
	// 宝盒
	luckyBoxParam, err := params.FindLuckyBox()
	if err != nil {
		return nil, err
	}
	// 未开始宝盒活动时，不返回宝盒礼物，测试房间除外
	if luckyBoxParam.Name == "" || (!luckyBoxParam.IsOpen() && !luckyBoxParam.IsAllowRoom(resp.roomID)) {
		return nil, nil
	}
	if !luckyBoxParam.IsShowInGifts() {
		return nil, nil
	}

	luckyBoxGift := &metaGift{
		Gift: gift.Gift{
			Type:         gift.TypeSpecial,
			Name:         luckyBoxParam.Name,
			Icon:         luckyBoxParam.IconURL,
			IconActive:   luckyBoxParam.IconActiveURL,
			Intro:        luckyBoxParam.Intro,
			IntroIcon:    luckyBoxParam.IntroIconURL,
			IntroOpenURL: luckyBoxParam.IntroOpenURL,
			Price:        luckyBoxParam.Price,
			LabelIcon:    luckyBoxParam.LabelIconURL,
		},
		SpecialType: gift.SpecialTypeLuckyBox,
		PriceType:   luckyBoxParam.PriceType,
		position:    luckyBoxParam.Position,
	}
	return luckyBoxGift, nil
}

func (resp *metaResp) findGiftGashapon() (*metaGift, error) {
	if !resp.room.IsOpen() {
		// 直播间未开播，不返回超能魔方入口
		return nil, nil
	}
	// WORKAROUND: Android < 6.3.5 和 iOS < 6.3.5 的版本不在礼物列表返回超能魔方
	if resp.equip.IsOldApp(goutil.AppVersions{IOS: "6.3.5", Android: "6.3.5"}) {
		return nil, nil
	}

	config, err := params.FindGashapon()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !config.IsShowInGifts() {
		return nil, nil
	}
	// Web 端兼容
	if !resp.equip.FromApp && config.WebOldVersion {
		return nil, nil
	}

	buff, err := gift.FindGashaponBuff(resp.roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	gashaponGift := &metaGift{
		Gift: gift.Gift{
			Type:         gift.TypeSpecial,
			Name:         config.Name,
			Icon:         config.IconURL,
			IconActive:   config.IconActiveURL,
			Intro:        config.Intro,
			IntroIcon:    config.IntroIconURL,
			IntroOpenURL: config.IntroOpenURL,
			Price:        config.Price,
			LabelIcon:    config.LabelIconURL,
		},
		SpecialType: gift.SpecialTypeGashapon,
		Buff:        buff,
		OpenURL:     config.OpenURL,
		position:    config.Position,
	}

	return gashaponGift, nil
}

func (resp *metaResp) findFansBox() {
	if !resp.hasType(metaTypeFansBox) || !livefansbox.EnableFansBox() || !resp.room.HaveMedal() {
		return
	}

	// 查询直播间当前粉丝团宝箱任务
	task, err := livefansboxtask.FindOrCreate(resp.roomID, goutil.TimeNow())
	if err != nil {
		logger.WithField("room_id", resp.roomID).Errorf("查询直播间当前粉丝团宝箱任务出错：%v", err)
		return
	}
	// 查询粉丝团宝箱信息
	currentBoxInfo, err := livefansbox.FindFansBoxByLevel(task.Level)
	if err != nil {
		logger.WithField("level", task.Level).Errorf("查询粉丝团宝箱信息出错：%v", err)
		return
	}
	if currentBoxInfo == nil {
		logger.WithField("level", task.Level).Error("未查询到对应等级的粉丝团宝箱信息")
		return
	}
	resp.FansBox = &fansBoxEntry{
		Name:    "粉团宝箱",
		IconURL: storage.ParseSchemeURL(config.Conf.Params.MedalParams.FansBoxDefaultIcon),
		BoxTask: boxTask{
			ID:            task.ID,
			TargetEnergy:  currentBoxInfo.Energy,
			CurrentEnergy: min(task.Energy, currentBoxInfo.Energy),
			Status:        task.Status,
		},
	}
}

// ActionMeta 直播间额外信息
/**
 * @api {get} /api/v2/chatroom/meta 直播间额外信息
 * @apiDescription 直播间额外信息，用户直播间内从未登录到登录状态，需要重新请求该接口获取需要的信息，断网恢复联网状态这类重试的请求不需要
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号，这里只根据房间号查询这个房间的 meta 信息，未检查房间是否存在
 * @apiParam {Number} [type=0] 返回的信息筛选，\
 *   0：认为未传该值返回完整响应，\
 *   1：只返回 gifts，\
 *   2: 只返回当前房间勋章，\
 *   3: 只返回互动信息，\
 *   4: 只返回活动推荐，\
 *   5: 只返回小时榜数据，\
 *   6: 只返回直播间小窗, 半窗配置，\
 *   7: 只返回扭蛋 buff 状态，\
 *   8: 只返回直播间 PK 信息，\
 *   9: 只返回直播间礼物墙信息，\
 *   10: 只返回直播间礼物红包信息，\
 *   11: 返回常驻兑换商城和等级权益商城入口，\
 *   12: 只返回 pia 戏功能入口，\
 *   13: 只返回一起送相关信息，\
 *   14: 返回快捷送礼相关信息，\
 *   15: 返回直播间房管和禁言用户（游客不返回禁言用户），\
 *   16: 直播间配置信息，\
 *   17: 返回喵喵福袋关相关信息，\
 *   18: 返回主播心愿单功能入口，\
 *   19: 返回直播间宝盒入口，\
 *   20: 返回主播连线信息，\
 *   21: 返回粉丝团宝箱信息
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "gifts":[{
 *         "title": "礼物",
 *         "data": [
 *           {
 *             "type": 1,
 *             "gift_id": 1,
 *             "name": "药丸",
 *             "icon_url": "https://static-test.maoercdn.com/gifts/icons/001.png",
 *             "icon_active_url": "https://static-test.maoercdn.com/gifts/icons/active/001.webp", // 礼物被选中时显示的动画
 *             "intro": "简介",
 *             "intro_icon_url": "简介图标", // 可能为空
 *             "intro_open_url": "简介链接地址", // 可能为空
 *             "comboable": 1, // 1 用户连击礼物；2 直播间连击礼物（安卓 < 6.0.7, iOS < 6.0.7 的版本不下发直播间连击礼物）
 *             "effect_url": "https://static-test.maoercdn.com/gifts/effects/001.webp;http://static-test.maoercdn.com/gifts/effects/001.webp", // 特效会返回多个
 *             "web_effect_url": "https://static-test.maoercdn.com/gifts/effects/001.webm", // PC 端显示的特效
 *             "effect_duration": 5000, // 购买礼物的房间内特效持续时间（毫秒）
 *             "price": 6,
 *             "noble_level": 0,
 *             "order": 1,
 *             "label_icon_url": "https://static-test.maoercdn.com/gifts/labels/001.png", // 角标
 *             "allowed_nums": [1, 10, 100, -1], // 允许的送礼次数， -1 代表可以自定义，没有该字段则使用默认情况
 *             "is_lucky": true, // 是否是随机礼物，false 和没有该字段说明不是随机礼物，true 说明是随机礼物
 *             "button": {
 *               "type": 1, // 按钮类型 1: 跳转按钮
 *               "text": "aa", // 按钮文本
 *               "url": "url", // 跳转链接
 *             },
 *             "sponsor": { // 冠名信息，没有冠名没有该字段
 *               "icon_url": "https://static-test.maoercdn.com/gifts/labels/001.png"
 *             }
 *           },
 *           {
 *             "type": 1, // 根据是否有 upgrade 字段判断是否为可升级礼物
 *             "gift_id": 1111,
 *             "name": "礼物名称",
 *             "icon_url": "https://static-test.maoercdn.com/gifts/icons/001.png",
 *             "icon_active_url": "https://static-test.maoercdn.com/gifts/icons/active/001.webp", // 礼物被选中时显示的动画
 *             "intro": "简介",
 *             "intro_icon_url": "简介图标", // 可能为空
 *             "intro_open_url": "简介链接地址", // 可能为空
 *             "comboable": 1, // 1 用户连击礼物；2 直播间连击礼物（安卓 < 6.0.7, iOS < 6.0.7 的版本不下发直播间连击礼物）
 *             "effect_url": "https://static-test.maoercdn.com/gifts/effects/001.webp;http://static-test.maoercdn.com/gifts/effects/001.webp", // 特效会返回多个
 *             "web_effect_url": "https://static-test.maoercdn.com/gifts/effects/001.webm", // PC 端显示的特效
 *             "effect_duration": 5000, // 购买礼物的房间内特效持续时间（毫秒）
 *             "price": 6, // 礼物价格，单位：钻石
 *             "noble_level": 0,
 *             "order": 1,
 *             "label_icon_url": "https://static-test.maoercdn.com/gifts/labels/001.png", // 角标
 *             "allowed_nums": [1, 10, 100, -1], // 允许的送礼次数， -1 代表可以自定义，没有该字段则使用默认情况
 *             "is_lucky": false, // 是否是随机礼物，false 和没有该字段说明不是随机礼物，true 说明是随机礼物
 *             "button": {
 *               "type": 1, // 按钮类型 1: 跳转按钮
 *               "text": "aa", // 按钮文本
 *               "url": "url", // 跳转链接
 *             },
 *             "upgrade": { // 升级礼物信息，仅升级礼物下发
 *               "series_name": "系列名",
 *               "discount": { // 折扣信息，没有折扣时不下发
 *                 "label_icon_url": "https://static-test.maoercdn.com/gifts/labels/001.png",
 *               },
 *               "upgrade_num": { // 集齐礼物后不下发
 *                 "remain_upgrade_num": 1, // 剩余可升级次数
 *                 "upgrade_gift_num": 12, // 本次升级所需礼物数量
 *                 "gift_num": 10 // 本次升级已累积赠送礼物数量
 *               },
 *               "gifts": [ // 升级礼物列表
 *                 {
 *                   "upgrade_type": 1, // 升级类型，1：初始，2：终极，3：升级
 *                   "type": 1,
 *                   "gift_id": 1111,
 *                   "name": "礼物名称",
 *                   "icon_url": "http://static-test.maoercdn.com/xxx/xxx.png", // 礼物栏图标
 *                   "small_icon_url": "http://static-test.maoercdn.com/xxx/xxx.png", // 升级栏小图标
 *                   "price": 6, // 礼物价格，单位：钻石
 *                   "comboable": 1 // 1 用户连击礼物；2 直播间连击礼物
 *                 },
 *                 {
 *                   "upgrade_type": 2, // 升级类型，1：初始，2：终极，3：升级
 *                   "type": 11,
 *                   "gift_id": 2222,
 *                   "name": "礼物名称",
 *                   "icon_url": "http://static-test.maoercdn.com/xxx/xxx.png",
 *                   "small_icon_url": "http://static-test.maoercdn.com/xxx/xxx.png",
 *                   "price": 10, // 礼物价格，单位：钻石
 *                   "comboable": 1, // 1 用户连击礼物；2 直播间连击礼物
 *                   "lock": 1 // 0 或不下发：已解锁，1：未解锁
 *                 },
 *                 {
 *                   "upgrade_type": 3, // 升级类型，1：初始，2：终极，3：升级
 *                   "type": 11,
 *                   "gift_id": 3333,
 *                   "name": "礼物名称",
 *                   "icon_url": "http://static-test.maoercdn.com/xxx/xxx.png",
 *                   "small_icon_url": "http://static-test.maoercdn.com/xxx/xxx.png",
 *                   "price": 10, // 礼物价格，单位：钻石
 *                   "comboable": 1, // 1 用户连击礼物；2 直播间连击礼物
 *                   "lock": 1 // 0 或不下发：已解锁，1：未解锁
 *                   "rate": "3%" // 获得概率（仅升级类型为 3 时下发）
 *                 }
 *               ]
 *             }
 *           },
 *           {
 *             "type": 1, // 根据是否有 black_card 字段判断是否为黑卡钻石皮肤基础礼物
 *             "gift_id": 1111,
 *             "name": "礼物名称",
 *             "icon_url": "https://static-test.maoercdn.com/gifts/icons/001.png",
 *             "icon_active_url": "https://static-test.maoercdn.com/gifts/icons/active/001.webp", // 礼物被选中时显示的动画
 *             "intro": "简介",
 *             "intro_icon_url": "简介图标", // 可能为空
 *             "intro_open_url": "https://www.missevan.com/mevent/900?from_room_id=__ROOM_ID__", // 跳转链接，支持模版变量，当下发时需要支持跳转
 *             "comboable": 1, // 1 用户连击礼物；2 直播间连击礼物（安卓 < 6.0.7, iOS < 6.0.7 的版本不下发直播间连击礼物）
 *             "effect_url": "https://static-test.maoercdn.com/gifts/effects/001.webp;http://static-test.maoercdn.com/gifts/effects/001.webp", // 特效会返回多个
 *             "web_effect_url": "https://static-test.maoercdn.com/gifts/effects/001.webm", // PC 端显示的特效
 *             "effect_duration": 5000, // 购买礼物的房间内特效持续时间（毫秒）
 *             "price": 6, // 礼物价格，单位：钻石
 *             "noble_level": 0,
 *             "order": 1,
 *             "label_icon_url": "https://static-test.maoercdn.com/gifts/labels/001.png", // 角标
 *             "allowed_nums": [1, 10, 100, -1], // 允许的送礼次数， -1 代表可以自定义，没有该字段则使用默认情况
 *             "is_lucky": false, // 是否是随机礼物，false 和没有该字段说明不是随机礼物，true 说明是随机礼物
 *             "black_card": { // 黑卡钻石皮肤礼物信息，仅黑卡钻石皮肤基础礼物下发
 *               "level": 1, // 解锁黑卡钻石皮肤礼物需要的黑卡等级
 *               "base_gift": { // 黑卡钻石皮肤基础礼物，无黑卡钻石皮肤基础礼物时不下发
 *                 "type": 1, // 礼物类型
 *                 "gift_id": 1111,
 *                 "name": "礼物名称",
 *                 "icon_url": "http://static-test.maoercdn.com/xxx/xxx.png", // 礼物图标
 *                 "small_icon_url": "http://static-test.maoercdn.com/xxx/xxx.png", // 礼物小图标
 *                 "price": 6, // 礼物价格，单位：钻石
 *                 "comboable": 1, // 1: 用户连击礼物；2: 直播间连击礼物
 *                 "icon_active_url": "http://static-test.maoercdn.com/gifts/icons/active.png" // 礼物被选中时显示的动画
 *               },
 *               "gift": { // 黑卡钻石皮肤礼物，无黑卡钻石皮肤礼物时不下发
 *                 "type": 12,
 *                 "gift_id": 10343,
 *                 "name": "礼物名称 1",
 *                 "icon_url": "http://static-test.maoercdn.com/xxx/xxx.png",
 *                 "small_icon_url": "http://static-test.maoercdn.com/xxx/xxx.png",
 *                 "price": 6,
 *                 "comboable": 1,
 *                 "icon_active_url": "http://static-test.maoercdn.com/gifts/icons/active.png" // 礼物被选中时显示的动画
 *               }
 *             }
 *           },
 *           { // 红包玩法
 *             "gift_id": 0, // 红包无礼物 ID
 *             "type": 0, // 0: 特殊礼物（用于红包、宝盒等玩法）
 *             "special_type": "red_packet", // type = 0 时下发该字段，用于区分具体的礼物类型
 *             "name": "红包",
 *             "price": 100 // 礼物红包起始价格（单位：钻石）
 *             "price_type": 1, // 0: 固定价格（不下发） 1: 最低价格（展示时显示多少价格起）
 *             "icon_url": "http://static-test.maoercdn.com/gifts/icons/001.png", // 礼物红包图（用于直播间礼物面板）
 *             "icon_active_url": "https://static-test.maoercdn.com/gifts/icons/active/001.webp", // 礼物被选中时显示的动画
 *             "label_icon_url": "http://static-test.maoercdn.com/gifts/icons/corner.png", // 角标
 *             "intro": "简介",
 *             "intro_icon_url": "简介图标",
 *             "intro_open_url": "简介链接地址",
 *           },
 *           { // 宝盒玩法
 *             "gift_id": 0, // 宝盒无礼物 ID
 *             "type": 0, // 0: 特殊礼物（用于红包、宝盒等玩法）
 *             "special_type": "lucky_box", // type = 0 时下发该字段，用于区分具体的礼物类型
 *             "name": "xx宝盒", // 用于宝盒入口（礼物面板普通礼物列表中）中显示的宝盒名称
 *             "price": 100, // 宝盒起始价格（单位：钻石）
 *             "price_type": 1, // 0: 固定价格 1: 最低价格（展示时显示多少价格起）
 *             "icon_url": "https://static-test.maoercdn.com/gifts/icons/001.png", // 礼物宝盒图标（用于直播间礼物面板）
 *             "icon_active_url": "https://static-test.maoercdn.com/gifts/icons/active/001.webp", // 礼物被选中时显示的动画
 *             "label_icon_url": "https://static-test.maoercdn.com/gifts/labels/001.png", // 角标
 *             "intro": "简介",
 *             "intro_icon_url": "简介图标", // 可能为空
 *             "intro_open_url": "简介链接地址", // 可能为空
 *           },
 *           { // 超能魔方
 *             "gift_id": 0, // 魔方无礼物 ID
 *             "type": 0, // 0: 特殊礼物（用于红包、宝盒等玩法）
 *             "special_type": "gashapon", // type = 0 时下发该字段，用于区分具体的礼物类型
 *             "name": "超能魔方/xx礼物", // 用于魔方入口（礼物面板普通礼物列表中）中显示的魔方名称
 *             "price": 100, // 魔方价格（单位：钻石）
 *             "price_type": 0, // 0: 固定价格 1: 最低价格（展示时显示多少价格起）
 *             "open_url": "https://www.uat.maoercdn.com/mevent/160?room_id=__ROOM_ID__", // 魔方购买地址，支持模板变量，和小窗一样
 *             "icon_url": "https://static-test.maoercdn.com/gifts/icons/001.png", // 魔方图标（用于直播间礼物面板）
 *             "icon_active_url": "https://static-test.maoercdn.com/gifts/icons/active/001.webp", // 魔方被选中时显示的动画
 *             "label_icon_url": "https://static-test.maoercdn.com/gifts/labels/001.png", // 角标
 *             "intro": "简介",
 *             "intro_icon_url": "简介图标", // 可能为空
 *             "intro_open_url": "简介链接地址", // 可能为空
 *             "buff": { // 没有 buff 不返回，buff 存在时优先使用 buff 中的礼物图标及名称，并使用倍率 label_intro 本地构建 label
 *               "type": 1, // 0: 能量值 buff; 1: 狂欢 buff
 *               "gift_id": 1,
 *               "name": "gift_name",
 *               "icon_url": "https://static-test.maoercdn.com/gifts/icons/10004.png",
 *               "icon_active_url": "https://static-test.maoercdn.com/gifts/icons/active/001.webp",
 *               "buff_duration": 180000, // 总时长，毫秒
 *               "remain_duration": 1000, // 剩余时间，毫秒
 *               "multiplier": "1.5", // 倍率
 *               "label_intro": ["2.5 倍", "狂欢"], // APP 需要轮播，WEB 拼接内容显示
 *               "end_time": 1716441600 // 狂欢结束时间，仅 type=1 时下发，秒级时间戳
 *             }
 *           },
 *           ...
 *         ]
 *       }, {
 *         "title": "贵族",
 *         "data": [
 *           {
 *             "gift_id": 2,
 *             "name": "寿司",
 *             "icon_url": "https://static-test.maoercdn.com/gifts/icons/002.png",
 *             "icon_active_url": "https://static-test.maoercdn.com/gifts/icons/active/002.webp",
 *             "intro": "简介",
 *             "notify_duration": 2000, // 通知持续时间（毫秒），没有该字段则认为默认 5000ms
 *             "price": 10,
 *             "noble_level": 1, // 这里面的礼物 noble_level 不为 0
 *             "order": 2
 *           },
 *           {
 *             "gift_id": 2,
 *             "name": "diy 礼物",
 *             "icon_url": "https://static-test.maoercdn.com/gifts/icons/002.png",
 *             "icon_active_url": "https://static-test.maoercdn.com/gifts/icons/active/002.webp",
 *             "intro": "简介",
 *             "notify_duration": 2000, // 通知持续时间（毫秒），没有该字段则认为默认 5000ms
 *             "price": 10,
 *             "vip_type": 2,
 *             "noble_level": 1, // 这里面的礼物 noble_level 不为 0
 *             "order": 3,
 *             "diy_entry": { // DIY 礼物入口，有此字段说明此礼物有 DIY 模块
 *               "image_url": "https://static-test.maoercdn.com/entry.png",
 *             }
 *           }
 *         ]
 *       }, {
 *         "title": "专属", // iOS <= 6.0.0，安卓 <= 6.0.0 的版本或者无专属礼物时不下发该结构
 *         "data": [
 *           {
 *             "gift_id": 70001,
 *             "type": 7,
 *             "name": "专属礼物",
 *             "icon_url": "https://static-test.maoercdn.com/gifts/icons/70001.png",
 *             "icon_active_url": "https://static-test.maoercdn.com/gifts/icons/active/70001.webp",
 *             "intro": "麻辣牛肉干",
 *             "price": 10,
 *             "order": 2
 *           },
 *           { // 列出用户当前已解锁的黑卡钻石皮肤礼物。黑卡钻石皮肤礼物 type 为 12，客户端通过调用 /api/v2/user/status/get 获取用户当前的黑卡等级，只有当该等级大于或等于礼物 black_card.level 时才可用（不可用时展示为引导用户去升级的样式，礼物 tab 和专属 tab 同理）
 *             "gift_id": 10339,
 *             "type": 12,
 *             "name": "黑卡钻石皮肤礼物 1",
 *             "icon_url": "https://static-test.maoercdn.com/gifts/icons/70001.png",
 *             "icon_active_url": "https://static-test.maoercdn.com/gifts/icons/active/70001.webp",
 *             "intro": "麻辣牛肉干",
 *             "price": 10,
 *             "order": 3,
 *             "black_card": { // 黑卡钻石皮肤礼物信息
 *               "level": 1 // 解锁黑卡钻石皮肤礼物需要的黑卡等级
 *             }
 *           },
 *           {
 *             "gift_id": 10343,
 *             "type": 12,
 *             "name": "黑卡钻石皮肤礼物 2",
 *             "icon_url": "https://static-test.maoercdn.com/gifts/icons/70001.png",
 *             "icon_active_url": "https://static-test.maoercdn.com/gifts/icons/active/70001.webp",
 *             "intro": "麻辣牛肉干",
 *             "price": 10,
 *             "order": 4,
 *             "black_card": {
 *               "level": 1
 *             }
 *           },
 *           ...
 *         ]
 *       }, {
 *         "title": "粉丝",
 *         "data": [
 *           {
 *             "gift_id": 3,
 *             "name": "牛肉干",
 *             "icon_url": "https://static-test.maoercdn.com/gifts/icons/002.png",
 *             "icon_active_url": "https://static-test.maoercdn.com/gifts/icons/active/002.webp",
 *             "intro": "麻辣牛肉干",
 *             "notify_duration": 2000, // 通知持续时间（毫秒），没有该字段则认为默认 5000ms
 *             "price": 10,
 *             "medal_level": 1, // 这里面的礼物 medal_level 不为 0
 *             "order": 2,
 *             "is_super_fan": true // 是否是超粉专属礼物，true 则是超粉专属礼物，不是则不返回该字段
 *           },
 *           ...
 *         }]
 *       },
 *       "gifts_extra": []  // 这里返回不在礼物列表中，但是可能在聊天消息中出现的礼物，可能是上面的任何一种结构, 主要是 web 匹配特效使用
 *       "medal": { // 粉丝勋章相关，不查询不返回此字段
 *         "user_medal": { // 请求用户当前房间的勋章，如果未查询到不返回此字段
 *           "name": "123",
 *           "level": 1
 *         },
 *         "medal_gift": { // 一键获取粉丝勋章的礼物信息，仅在未获得勋章时返回
 *           "gift_id": 1,
 *           "name": "礼物 A",
 *           "price": 1234, // 单位：钻
 *           "icon_url": "http://static.example.com/gift/001.png",
 *           "discount": { // 折扣信息，没有需要展示的折扣不下发此字段
 *             "label_icon_url": "http://static.example.com/gift/label.png" // 折扣角标，收到开通粉丝勋章 ws 消息时需要移除角标展示
 *           }
 *         }
 *       },
 *       "interaction": { // 当前房间进行中的互动，如果未查询到不返回此字段
 *         "vote":{
 *           "vote_id": "5ab9d5f1bc9b53298ce5a5a9",
 *           "title": "pick 你喜欢的牛排",
 *           "duration": 600000, // 用户投票设置时长，单位毫秒，再来一局时使用
 *           "remain_duration": 100000, // 投票剩余时长，单位毫秒, 投票公示期时值为 0
 *           "announce_duration": 10000, // 投票结果公示时长，单位毫秒
 *           "content": [
 *             {
 *               "gift_id": 10001,
 *               "description": "生",
 *               "icon_url": "https://static-test.maoercdn.com/gifts/icons/10001.png",
 *               "web_icon_url": "https://static-test.maoercdn.com/gifts/icons/10001-web.png",
 *               "color": "#FE929B",
 *               "num": 10
 *             },
 *             {
 *               "gift_id": 10002,
 *               "description": "三分熟",
 *               "icon_url": "https://static-test.maoercdn.com/gifts/icons/10002.png",
 *               "web_icon_url": "https://static-test.maoercdn.com/gifts/icons/10002-web.png",
 *               "color": "#B6E58B",
 *               "num": 15
 *             },
 *             {
 *               "gift_id": 10003,
 *               "description": "五分熟",
 *               "icon_url": "https://static-test.maoercdn.com/gifts/icons/10003.png",
 *               "web_icon_url": "https://static-test.maoercdn.com/gifts/icons/10003-web.png",
 *               "color": "#F5CC6E",
 *               "num": 20
 *             },
 *             {
 *               "gift_id": 10004,
 *               "description": "七分熟",
 *               "icon_url": "https://static-test.maoercdn.com/gifts/icons/10004.png",
 *               "web_icon_url": "https://static-test.maoercdn.com/gifts/icons/10004-web.png",
 *               "color": "#BEB2FF",
 *               "num": 20
 *             }
 *           ]
 *         }
 *       },
 *       "pk": {
 *         "show": 1, // 是否展示直播 PK 入口, 仅当主播在自己直播间时会下发, 0 或该字段不存时无直播入口
 *         "icon_url": "https://static-test.maoercdn.com/pk/icons.png", // PK 入口图标
 *         "detail": { // 下发正在进行中的 PK 信息，无进行中的 PK 不下发该字段，且指定 PK 邀请中也不下发该字段
 *           "pk_id": "5ab9d5f1bc9b53298ce5a5a9", // 匹配中时不下发该字段
 *           "type": 0, // 0: 随机 PK; 1: 指定 PK; 2: 排位赛
 *           "status": 0, // 0: 随机 PK 匹配中, 指定 PK 邀请/被邀请中; 1: PK 进行中; 2: PK 惩罚期进行中; 3: PK 普通连麦期; 4: PK 结束
 *           "mute": 0, // 0: 未静音对手; 1: 已静音对手
 *           "start_time": 16414300000000, // PK 开始时间, 单位毫秒
 *           "remain_duration": 200, // 距离 PK 匹配超时、结束、惩罚结束倒计时, 单位毫秒
 *           "duration": 60000, // PK 匹配期、进行期、惩罚期总时长, 单位毫秒
 *           "result": 0, // PK 胜负结果, PK 打榜结束下发, 0: 失败; 1: 胜利; 2: 平局
 *           "fighters": [ // 第一位是本房间主播信息 (若该直播间状态为 PK 匹配中, 该字段是空数组)
 *             {
 *               "room_id": 1,
 *               "creator_id": 1,
 *               "score": 1,
 *               "name": "room1",
 *               "creator_username": "name1",
 *               "creator_iconurl": "http://aaa.bbb.ccc/test56.png",
 *               "top_fans": [ // 粉丝前三排行榜
 *                 {
 *                   "user_id": 56,
 *                   "username": "test56",
 *                   "iconurl": "http://aaa.bbb.ccc/test56.png"
 *                 }
 *               ]
 *             },
 *             {
 *               "room_id": 2,
 *               "creator_id": 2,
 *               "score": 2,
 *               "name": "room1",
 *               "creator_username": "name1",
 *               "creator_iconurl": "http://aaa.bbb.ccc/test56.png",
 *               "top_fans": [
 *                 {
 *                   "user_id": 57,
 *                   "username": "test57",
 *                   "iconurl": "http://aaa.bbb.ccc/test57.png"
 *                 },
 *                 {
 *                   "user_id": 75,
 *                   "username": "test75",
 *                   "iconurl": "http://aaa.bbb.ccc/test75.png"
 *                 }
 *               ]
 *             }
 *           ]
 *         },
 *         "invitation": { // 指定 PK 邀请信息, 仅主播端邀请/被邀请时下发
 *           "match_id": "5ab9d5f1bc9b53298ce5a5a9",
 *           "invited": 0, // 0: 邀请发起方; 1: 被邀请方
 *           "duration":  10000, // 指定 PK 邀请等待期总时长，单位毫秒
 *           "remain_duration": 0, // 指定 PK 邀请等待期倒计时，单位毫秒
 *           "fighting_duration": 50000, // 指定 PK 打榜总时长，单位毫秒
 *           "create_time": 1584808200, // 单位秒
 *           "from_room": { // 邀请方直播间信息, invited 为 1 时下发该字段
 *             "room_id": 10,
 *             "creator_id": 10,
 *             "creator_username": "username",
 *             "creator_iconurl": "user_iconurl",
 *             "statistics": {
 *               "score": 0 // 直播间热度
 *             }
 *           },
 *           "to_room": { // 被邀请方直播间信息, invited 为 0 时下发该字段
 *             "room_id": 10,
 *             "creator_id": 10,
 *             "creator_username": "username",
 *             "creator_iconurl": "user_iconurl"
 *           }
 *         }
 *       },
 *       "multi_connect": { // 主播连线相关信息
 *         "show": 1, // 是否展示主播连线入口，0 或该字段不存时无主播连线入口，仅主播在自己直播间时会下发
 *         "icon_url": "https://static-test.maoercdn.com/multi-connect/icons.png", // 入口图标
 *         "detail": { // 下发正在进行中的主播连线信息，无进行中连线不下发该字段
 *           "group_id": 1, // 连线组 ID
 *           "status": 1, // 连线状态；1: 连线中；2: 已结束
 *           "duration": 60000, // 连线总时长, 单位：毫秒
 *           "mute_room_ids": [3,12,223344], // 静音连线直播间 ID 列表
 *           "mic_off_room_ids": [], // 闭麦直播间 ID 列表
 *           "config": {
 *             "user_rank": {
 *               "granted": 1, // 是否有权限展示和隐藏榜单，0: 没有权限；1: 有权限
 *               "enable": 1 // 是否打开在线用户榜单开关，0: 未打开；1: 已打开
 *             },
 *             "cross_msg": {
 *               "granted": 1, // 是否有权限开启消息互通，0: 没有权限；1: 有权限
 *               "enable": 1 // 是否开启消息互通，0: 未打开；1: 已打开
 *             }
 *           },
 *           "members": [ // 连线成员列表，首位总是当前麦位
 *             {
 *               "index": 1, // 连线序号
 *               "role": 1, // 角色；1: 主麦；2: 连线成员
 *               "score": 111, // 礼物积分
 *               "room": { // 房间信息
 *                 "room_id": 152,
 *                 "creator_id": 12345,
 *                 "creator_username": "1234",
 *                 "creator_iconurl": "http://aaa.bbb.ccc/test.png",
 *                 "status": {
 *                   "attention": true // 是否关注了主播
 *                 }
 *               }
 *             },
 *             ...
 *           ]
 *         }
 *       },
 *       "events": [
 *         {
 *           "cover": "封面图",
 *           "url": "链接",
 *           "show_close": true // 是否可关闭
 *         }
 *       ],
 *       "hour_rank":{
 *         "rank": 0, // 0 说明不在榜内，其他情况代表榜上
 *         "rank_up": 100, // 不在榜内代表上榜需要的分值，在榜内不是第一名是排名上升的值，第一名是与第二名的差值
 *       }
 *       "popups": {
 *         "fold": true, // 是否默认折叠, true: 折叠; false: 展开
 *         "data": [{
 *           "mini_url": "http://fm.example.com/mini?webview=1", // 小的 webview 显示
 *           "image_url": "http://fm.example.com/image.png", // 表示 webview 加载完成前 / 加载错误显示的图
 *           "fold_image_url": "http://fm.example.com/fold_image?webview=1", // 小窗折叠图
 *           "full_url": "http://fm.example.com/full?webview=1", // full_url 和 open_url 互斥，分别代表半窗的 url 和在新页面打开的 url
 *           "open_url": "http://fm.example.com/open?webview=1" // full_url 和 open_url 互斥，同时只会返回一个字段
 *         }]
 *       },
 *       "gashapon": { // 玩法栏入口，没有入口时不下发
 *         "default_icon_url": "https://static-test.maoercdn.com/gifts/icons/10004.png",
 *         "label_icon_url": "https://static-test.maoercdn.com/gifts/labels/001.png", // 角标
 *         "open_url": "https://www.uat.maoercdn.com/mevent/160?room_id=__ROOM_ID__&user_id=__USER_ID__&catalog_id=__CATALOG_ID__", // 支持模板变量，和小窗一样
 *         "buff": {  // 没有 buff 不返回，优先使用 buff 中的礼物图标及名称，并使用倍率 label_intro 本地构建 label
 *           "type": 1, // 0: 能量值 buff; 1: 狂欢 buff
 *           "name": "gift_name",
 *           "icon_url": "https://static-test.maoercdn.com/gifts/icons/10004.png",
 *           "buff_duration": 180000, // 总时长
 *           "remain_duration": 1000, // 剩余时间，毫秒
 *           "multiplier": "1.5", // 倍率
 *           "label_intro": ["1.5 倍", "2.5 倍狂欢"], // APP 需要轮播，WEB 拼接内容显示
 *           "end_time": 1716441600 // 狂欢结束时间，仅 type=1 时下发，秒级时间戳
 *         }
 *       },
 *       "gift_wall": { // 没有入口时，无该结构
 *         "activated_num": 1, // 已点亮数量
 *         "total_num": 20 // 本期上墙礼物总数
 *       },
 *       "red_packet": { // 房间内红包列表和礼物红包信息（礼物红包信息未下发时，端上使用本地写死的名称及图标做兜底）
 *         "name": "红包", // 用于发红包、抢红包消息中显示的红包名称
 *         "image_url": "http://static-test.maoercdn.com/live/redpacket/icons/v2.png" // 红包小图标（用于 APP 直播间左上角玩法栏展示的图标）
 *         "icon_url": "http://static-test.maoercdn.com/gifts/icons/001.png", // 礼物红包图（用于发红包、抢红包消息、直播间内红包展示的图标）
 *         "big_icon_url": "http://static-test.maoercdn.com/gifts/icons/001.png", // 礼物红包大图，用于红包从大到小的动画
 *         "list": [ // 房间内红包列表，为空时不显示红包领取入口
 *           {
 *             "red_packet_id": "abc", // 礼物红包 ID
 *             "type": 1, // 红包类型。1: 普通红包；2: 口令红包。一期仅下发 type = 1
 *             "corner_icon_url": "https://static-test.maoercdn.com/test/corner.png", // 角标图
 *             "price": 999, // 红包购买价格（单位：钻石）
 *             "gift_total_price": 1999, // 礼物总价值（单位：钻石）
 *             "skin_url": "https://static-test.maoercdn.com/test/skin.zip", // 红包皮肤资源压缩包地址
 *             "sender": { // 发红包用户的信息
 *               "user_id": 1, // 发送者用户 ID
 *               "username": "发送者用户名",
 *               "iconurl": "https://static-test.maoercdn.com/test/avatar.png" // 发送者用户头像
 *             },
 *             "gift_total_num": 233, // 礼物总数
 *             "gifts": [ // 红包的礼物列表
 *               {
 *                 "gift_id": 1,
 *                 "name": "礼物名称",
 *                 "icon_url": "https://static-test.maoercdn.com/test/test.png", // 礼物图标
 *                 "num": 233 // 礼物数量
 *               }
 *             ],
 *             "remain_duration": 60000, // 倒计时，单位：毫秒，0 为当前可抢
 *             "keyword": "谢谢老板" // 红包口令
 *           }
 *         ]
 *       },
 *       "redeem_shop": { // 常驻兑换商城的入口（主播端也需要支持），无该字段或者为 null 时，端上不显示该入口
 *         "name": "万事屋", // 兑换商城的名字
 *         "icon_url": "https://static-test.maoercdn.com/test/yorozuya.png", // 客户端兑换商城入口的图标
 *         "web_icon_url": "https://static-test.maoercdn.com/test/yorozuya.png", // 网页端兑换商城入口的图标
 *         "shop_url": "https://fm.missevan.com/some-url-to-the-shop" // 入口所对应的链接，客户端注意用 msr-0 处理
 *       },
 *       "privilege_shop": { // 等级权益商城的入口（主播端也需要支持），无该字段或者为 null 时，端上不显示该入口
 *         "name": "星享馆", // 商城的名字
 *         "icon_url": "https://static-test.maoercdn.com/test/privilege.png", // 客户端商城入口的图标
 *         "web_icon_url": "https://static-test.maoercdn.com/test/privilege.png", // 网页端商城入口的图标
 *         "shop_url": "https://fm.missevan.com/some-url-to-the-shop" // 入口所对应的链接，客户端注意用 msr-0 处理
 *       },
 *       "pia": { // pia 戏功能，仅主播端显示该入，不下发该字段则不显示 pia 戏入口
 *         "name": "pia 戏", // pia 戏入口名称
 *         "status": 1, // pia 戏状态，0: 未开始; 1: 进行中
 *         "duration": 10000 // 进入听剧标签的时长（未开始时不下发该字段），单位：毫秒
 *       },
 *       "multi_combo": {
 *         "status": 1, // 是否显示一起送快捷送礼图标 0: 不展示; 1: 展示
 *         "combos": [{ // 连击中的直播间连击礼物列表，不在连击中或不需要展示时不下发该字段
 *           "id": "5ef9c9002b9aed35f1607aa2",
 *           "gift": {
 *             "gift_id": 10002,
 *             "name": "连击礼物",
 *             "icon_url": "https://static-test.maoercdn.com/gifts/icons/10002.png",
 *             "price": 6
 *           },
 *           "num": 100, // 当前用户的累计赠送数量
 *           "total_num": 150, // 礼物连击的总数量，客户端根据送礼总金额（总数*礼物单价）和倒计时维护需要展示的信息，仅直播间连击礼物下发
 *           "achieved_num": 0, // 计算进度的档位起始数量，直播间连击计算进度的公式: (total_num - achieved_num) / (target_num - achieved_num)
 *           "target_num": 200, // 当前档位触发连击需要的总累计礼物数量
 *           "remain_time": 30000 // 连击剩余时间 (ms)
 *         }]
 *       },
 *       "shortcut_gift": { // 快捷送礼按钮，不下发该字段则不显示快捷送礼按钮
 *         "gift_id": 10001,
 *         "name": "礼物",
 *         "icon_url": "https://static-test.maoercdn.com/gifts/icons/10001.png",
 *         "price": 6, // 单位钻石
 *         "comboable": 1
 *       },
 *       "members": {
 *         "admin": [
 *           {
 *             "room_id": 112422405,
 *             "user_id": 3456837,
 *             "username": "virtual_test",
 *             "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *           }
 *         ],
 *         "mute": [ // 游客该项为空
 *           {
 *             "room_id": 112422405,
 *             "user_id": 3456838,
 *             "username": "test",
 *             "iconurl": "http://static.missevan.com/avatars/icon01.png",
 *             "expire_at": 1584808200 // 单位秒
 *           }
 *         ]
 *       },
 *       "config": { // 直播间配置信息，没有配置信息不下发
 *         "allow_hide_gift_effect": true // 用户是否可以屏蔽礼物特效
 *       },
 *       "lucky_bag_entry": { // 喵喵福袋入口，没有该字段不显示该入口
 *         "name": "喵喵福袋",
 *       },
 *       "lucky_bag": { // 当前直播间福袋信息，没有需要展示的福袋不返回该字段
 *         "lucky_bag_id": 1, // 福袋 ID
 *         "type": 1, // 福袋类型，1: 剧集福袋; 2: 实物福袋
 *         "status": 0, // 福袋状态 0: 待开奖; 1: 已开奖（发奖中）; 2: 已开奖（已发奖）
 *         "image_url": "https://static-test.maoercdn.com/luckybag.png", // 福袋图标
 *         "new_image_url": "https://static-test.maoercdn.com/live/luckybag/icon-v2.png", // 新福袋图标，仅供 APP 新版本使用，如不存在应使用 image_url 字段
 *         "big_image_url": "https://static-test.maoercdn.com/luckybag-big.png", // 福袋大图标
 *         "prize_icon_url": "https://static-test.maoercdn.com/icon.png", // 封面图，下发就显示在福袋上
 *         "remain_duration": 18000, // 开奖、公示剩余时间，如果倒计时结束 10s 后没有收到开奖消息总是隐藏，单位：毫秒
 *         "join_status": 1, // 当前用户是否参与抽奖 0: 未参与; 1: 已参与。主播不下发该状态
 *         "has_more": true // 是否显示更多福袋入口: true: 显示; false: 隐藏
 *       },
 *       "wish_list": { // 心愿单的入口，无该字段或者为 null 时不显示该入口
 *         "name": "心愿单"
 *       },
 *       "lucky_box": { // 宝盒的入口，无该字段或者为 null 时不显示该入口
 *         "name": "XX宝盒", // 宝盒名称
 *         "image_url": "http://static-test.maoercdn.com/live/redpacket/icons/v2.png", // 宝盒小图标 (用于 APP 直播间左上角玩法栏展示的图标)
 *         "label_icon_url": "https://static-test.maoercdn.com/gifts/labels/001.png" // 角标
 *       },
 *       "fans_box": { // 粉丝团宝箱的入口，无该字段或者为 null 时不显示该入口
 *         "name": "粉团宝箱", // 宝箱名称
 *         "icon_url": "http://static.example.com/icons/001.png", // 宝箱 icon
 *         "box_task": {
 *           "id": 1, // 任务 ID
 *           "target_energy": 10000, // 解锁宝箱所需能量值
 *           "current_energy": 1400, // 已达成的能量值
 *           "status": 0 // 宝箱完成状态，0：未完成；1：已完成
 *         }
 *       }
 *     }
 *   }
 */
func ActionMeta(c *handler.Context) (handler.ActionResponse, error) {
	resp := new(metaResp)
	err := resp.load(c)
	if err != nil {
		return nil, err
	}
	err = resp.findGifts(c)
	if err != nil {
		return nil, err
	}
	err = resp.findMedal()
	if err != nil {
		return nil, err
	}
	err = resp.findInteraction()
	if err != nil {
		return nil, err
	}
	err = resp.findPK()
	if err != nil {
		return nil, err
	}
	err = resp.findMultiConnect()
	if err != nil {
		return nil, err
	}
	err = resp.findEvents()
	if err != nil {
		return nil, err
	}
	err = resp.findHourRank()
	if err != nil {
		return nil, err
	}
	err = resp.findPopups()
	if err != nil {
		return nil, err
	}
	err = resp.findGashapon()
	if err != nil {
		return nil, err
	}
	err = resp.findGiftWall()
	if err != nil {
		return nil, err
	}
	err = resp.findRedPacket()
	if err != nil {
		return nil, err
	}
	err = resp.findShops()
	if err != nil {
		return nil, err
	}
	// NOTICE: pia 戏查询失败不影响其他信息的下发
	resp.findPia()
	resp.findMultiCombo()
	resp.findShortcutGift()
	resp.findMembers()
	resp.findConfig()
	resp.findLuckyBagEntry()
	resp.findLuckyBag()
	resp.findWishListEntry()
	resp.findLuckyBox()
	resp.findFansBox()
	return resp, nil
}
