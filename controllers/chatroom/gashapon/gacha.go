package gashapon

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"sort"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/liveshow"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/internal/biz/fansboxenergy"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/livedb/shop"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegashaponuserrank"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal/livemedalstats"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liverevenues"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/chatroom"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type gachaResp struct {
	User          *liveuser.Simple        `json:"user"`
	Bubble        *bubble.Simple          `json:"bubble,omitempty"`
	Balance       *utils.BalanceAfterSend `json:"balance"`
	MessagePrefix string                  `json:"message_prefix"`
	OpenURL       string                  `json:"open_url,omitempty"`
	Gifts         []*gift.NotifyGift      `json:"gifts"`
	GashaponNum   int                     `json:"gashapon_num"`
	GrandGiftID   int64                   `json:"grand_gift_id"`
}

type gashaponGachaInfo struct {
	GoodsID      int64           `json:"goods_id"`
	Name         string          `json:"name"`
	Contribution int             `json:"contribution"`
	Num          int             `json:"num"`
	OpenURL      string          `json:"open_url"`
	IconURL      string          `json:"icon_url"`
	Gifts        []*gashaponGift `json:"gifts"`
}

type gachaGashaponBroadcastPayload struct {
	Type             string                           `json:"type"`
	Event            string                           `json:"event"`
	RoomID           int64                            `json:"room_id"`
	Time             int64                            `json:"time"`
	CurrentRevenue   int64                            `json:"current_revenue"`
	User             *liveuser.Simple                 `json:"user"`
	Bubble           *bubble.Simple                   `json:"bubble"`
	Gashapon         *gashaponGachaInfo               `json:"gashapon"`
	GiftNotification *userappearance.GiftNotification `json:"gift_notification,omitempty"`
}

type gachaParam struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	GoodsID int64 `form:"goods_id" json:"goods_id"`

	uc mrpc.UserContext

	r *room.Room

	userID int64
	u      *liveuser.Simple
	uv     *vip.UserVip
	bubble *bubble.Simple
	config params.Gashapon

	goods         *livegoods.LiveGoods
	more          *livegoods.More
	messagePrefix string

	pool *gift.PoolGashapon

	drawResults []gift.DrawResult
	totalPrice  int64
	lgs         []*livegifts.LiveGift
	orderMore   *livetxnorder.MoreInfo

	balance *userapi.BalanceResp // 交易响应信息

	broadcastElems []*userapi.BroadcastElem
}

// ActionGashaponGacha 扭蛋购买
/**
 * @api {post} /api/v2/chatroom/gashapon/gacha 扭蛋购买
 * @apiDescription 购买扭蛋送随机礼物，两次购买的礼物无连击
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} goods_id 商品 ID
 *
 * @apiSuccessExample {json} 响应
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "user": {
 *           "user_id": 10,
 *           "username": "bless",
 *           "iconurl": "https://static-test.missevan.com/profile/123.png",
 *           "titles": [{
 *             "type": "staff",
 *             "name": "超管",
 *             "color": "#F45B41"
 *           }, {
 *             "type": "level",
 *             "level": 9
 *           }, {
 *             "type": "medal",
 *             "name": "独角兽",
 *             "level": 4
 *           }, {
 *             "type": "noble",
 *             "name": "新秀",
 *             "level": 2
 *           }, {
 *             "type": "avatar_frame",
 *             "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *           }, {
 *             "type": "identity_badge", // 身份铭牌
 *             "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *             "appearance_id": 10001 // 图标 ID
 *           }]
 *         },
 *         "bubble": { // 如果没有特殊气泡，这个字段不存在
 *           "type": "noble", // 气泡类型，当前是贵族气泡
 *           "noble_level": 2 // 使用对应等级的贵族气泡
 *         },
 *         "balance": {
 *           "balance": 11479968,
 *           "live_noble_balance": 283748,
 *           "live_noble_balance_status": 1
 *         },
 *         "message_prefix": "通过超能魔方", // 消息前缀。仅包含超能魔方、织梦奇境才可点击拉起魔方窗口，其他内容则直接使用 message_prefix 字段内容显示且不可点击拉起魔方窗口。
 *         "open_url": "https://www.uat.maoercdn.com/mevent/160?room_id=__ROOM_ID__", // 魔方购买地址，支持模板变量，和小窗一样
 *         "gifts": [{ // 抽出的礼物
 *           "gift_id": 90001,
 *           "name: "礼物名称",
 *           "icon_url": "https://static-test.missevan.com/gifts/icons/90001.png",
 *           "effect_url": "*.mp4;*.webp;*.webm", // 特效规则同通用广播特效
 *           "web_effect_url": "*.mp4;*.webp;*.webm",
 *           "effect_duration": 5000,
 *           "price": 6,
 *           "num": 1
 *         }, {
 *           "gift_id": 90002,
 *           "name: "礼物名称",
 *           "icon_url": "https://static-test.missevan.com/gifts/icons/90002.png",
 *           "effect_url": "*.mp4;*.webp;*.webm", // 特效规则同通用广播特效
 *           "web_effect_url": "*.mp4;*.webp;*.webm",
 *           "effect_duration": 5000,
 *           "price": 6,
 *           "num": 9
 *         }],
 *         "gashapon_num": 100, // 总抽取次数
 *         "grand_gift_id": 90001 // 大奖礼物 ID
 *       }
 *     }
 *
 * @apiSuccessExample {json} WebSocket 房间内消息
 *     [{
 *       "type": "gift",
 *       "event": "send",
 *       "room_id": 65261414,
 *       "user": {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.missevan.com/test.png",
 *         "titles": [{
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#F45B41"
 *         }, {
 *           "type": "level",
 *           "level": 9
 *         }, {
 *           "type": "medal",
 *           "name": "独角兽",
 *           "level": 4
 *         }, {
 *           "type": "noble",
 *           "name": "新秀",
 *           "level": 2
 *         }, {
 *           "type": "avatar_frame",
 *           "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *         }, {
 *           "type": "badge",
 *           "icon_url": "https://static-test.missevan.com/live/medalframes/3f12/level01_0_9_0_54.png",
 *           "appearance_id": 1
 *         }, {
 *           "type": "identity_badge", // 身份铭牌
 *           "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *           "appearance_id": 10001 // 图标 ID
 *         }]
 *       },
 *       "time": 1576744741101,
 *       "message_prefix": "通过超能魔方", // 消息前缀，字段不存在则不需要组合。仅包含超能魔方、织梦奇境才可点击拉起魔方窗口，其他内容则直接使用 message_prefix 字段内容显示且不可点击拉起魔方窗口。
 *       "open_url": "https://www.uat.maoercdn.com/mevent/160?room_id=__ROOM_ID__", // 魔方购买地址，支持模板变量，和小窗一样
 *       "gift": {
 *         "gift_id": 90001,
 *         "name": "魔法王冠",
 *         "icon_url": "https://static-test.missevan.com/gifts/icons/90001.png",
 *         "effect_url": "*.lottie;*.mp4", // 特效规则同通用广播特效
 *         "web_effect_url": "*.lottie;*.mp4'*.png",
 *         "effect_duration": 5000,
 *         "price": 6,
 *         "num": 1
 *       },
 *       "gift_notification": { // 用户佩戴的送礼通知皮肤，未佩戴送礼通知皮肤时不下发，客户端和前端使用默认送礼通知皮肤
 *         "username_color": "#F0F0F0",
 *         "text_color": "#F0F0F0",
 *         "frame_url": "https://static-test.maoercdn.com/live/gift/notification/blackcard-1.png"
 *       },
 *       "bubble": {
 *         "type": "noble",
 *         "noble_level": 2
 *       },
 *       "current_revenue": 612
 *     }, {
 *       "type": "gift",
 *       "event": "send",
 *       "room_id": 65261414,
 *       "user": {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.missevan.com/test.png",
 *         "titles": [{
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#F45B41"
 *         }, {
 *           "type": "level",
 *           "level": 9
 *         }, {
 *           "type": "medal",
 *           "name": "独角兽",
 *           "level": 4
 *         }, {
 *           "type": "noble",
 *           "name": "新秀",
 *           "level": 2
 *         }, {
 *           "type": "avatar_frame",
 *           "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *         }, {
 *           "type": "badge",
 *           "icon_url": "https://static-test.missevan.com/live/medalframes/3f12/level01_0_9_0_54.png",
 *           "appearance_id": 1
 *         }, {
 *           "type": "identity_badge", // 身份铭牌
 *           "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *           "appearance_id": 10001 // 图标 ID
 *         }]
 *       },
 *       "time": 1576744741101,
 *       "message_prefix": "通过超能魔方", // 消息前缀，字段不存在则不需要组合
 *       "gift": {
 *         "gift_id": 90001,
 *         "name": "魔法王冠",
 *         "icon_url": "https://static-test.missevan.com/gifts/icons/90001.png",
 *         "effect_url": "*.lottie;*.mp4", // 特效规则同通用广播特效
 *         "web_effect_url": "*.lottie;*.mp4'*.png",
 *         "effect_duration": 5000,
 *         "price": 6,
 *         "num": 1
 *       },
 *       "gift_notification": { // 用户佩戴的送礼通知皮肤，未佩戴送礼通知皮肤时不下发，客户端和前端使用默认送礼通知皮肤
 *         "username_color": "#F0F0F0",
 *         "text_color": "#F0F0F0",
 *         "frame_url": "https://static-test.maoercdn.com/live/gift/notification/blackcard-1.png"
 *       },
 *       "bubble": {
 *         "type": "noble",
 *         "noble_level": 2
 *       },
 *       "current_revenue": 612
 *     }]
 *
 * @apiSuccessExample {json} WebSocket 抽取超能魔方消息:
 *   {
 *     "type": "gashapon", // 目前（版本 6.3.5）暂时不会下发，仍以 gift 和 send 的形式下发，客户端需要先支持该 ws 类型
 *     "event": "gacha",
 *     "room_id": 1, // 当前直播间
 *     "user": {
 *       "user_id": 10,
 *       "username": "bless",
 *       "iconurl": "https://static-test.missevan.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *       "titles": [{
 *         "type": "staff",
 *         "name": "超管",
 *         "color": "#F45B41"
 *       }, {
 *         "type": "identity_badge", // 身份铭牌
 *         "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *         "appearance_id": 10001 // 图标 ID
 *       }]
 *     },
 *     "time": 1576744741101,
 *     "current_revenue": 1, // 本场榜中此用户的贡献值
 *     "gift_notification": { // 用户佩戴的送礼通知皮肤，未佩戴送礼通知皮肤时不下发，客户端和前端使用默认送礼通知皮肤
 *       "username_color": "#F0F0F0",
 *       "text_color": "#F0F0F0",
 *       "frame_url": "https://static-test.maoercdn.com/live/gift/notification/blackcard-1.png"
 *     },
 *     "bubble": { // 如果没有特殊气泡，这个字段为 null 或不存在
 *       "type": "message", // 气泡类型，聊天气泡 message
 *       "image_url": "https://static.maoercdn.com/live/bubble/image/001.png",
 *       "frame_url": "https://static.maoercdn.com/live/bubble/frame/001.png",
 *       "text_color": "#F0F0F0"
 *     },
 *     "gashapon": {
 *       "goods_id": 10,
 *       "name": "超能魔方",
 *       "contribution": 50, // 花费钻石, 需要根据该字段更新收益记录
 *       "num": 3, // 抽取数量
 *       "open_url": "https://www.uat.maoercdn.com/mevent/160?room_id=__ROOM_ID__", // 魔方购买地址，支持模板变量，和小窗一样
 *       "icon_url": "http://static.maoercdn.com/liveactivity/223/box/cover.png",
 *       "gifts": [ // 开出的奖品列表
 *         {
 *           "gift_id": 1, // 礼物 ID
 *           "gift_name": "典藏 1k 热度卡", // 礼物名
 *           "gift_icon_url": "http://static.maoercdn.com/liveactivity/223/box/prize.png", // 礼物图标
 *           "price": 100, // 礼物价格, 单位钻石
 *           "num": 1, // 礼物数量
 *           "effect_url": "*.mp4;*.webp;*.webm", // 特效规则同通用广播特效
 *           "web_effect_url": "*.mp4;*.webp;*.webm"
 *         },
 *         {
 *           "gift_id": 2, // 礼物 ID
 *           "gift_name": "1k 热度卡", // 礼物名
 *           "gift_icon_url": "http://static.maoercdn.com/liveactivity/223/box/prize.png", // 礼物图标
 *           "num": 2, // 礼物数量
 *           "price": 100, // 礼物价格, 单位钻石
 *           "effect_url": "*.mp4;*.webp;*.webm", // 特效规则同通用广播特效
 *           "web_effect_url": "*.mp4;*.webp;*.webm"
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 全局消息
 *     // 和普通送礼产生的飘屏一致
 *     {
 *       "type": "notify",
 *       "notify_type": "gift",
 *       "event": "send",
 *       "room_id": 65261414,
 *       "user": {
 *         ...
 *       },
 *       "gift": {
 *         ...
 *       },
 *       "notify_bubble": {
 *         "type": "custom",
 *         "image_url": "https://example.com/b128_0_10_0_100.png",
 *         "float": 1, // 如果 float 为 1, 则是悬停气泡，不存在或为 0 则是正常飘屏气泡
 *         "shine": 0 // 是否闪光，0 为不闪光，1 为闪光，字段不存在则默认不闪光
 *       },
 *       "message": "[用户昵称] 在转盘内百连获得 [礼物名称]，手气爆棚~"
 *     }
 *
 */
func ActionGashaponGacha(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGachaParam(c)
	if err != nil {
		return nil, err
	}
	err = param.gacha()
	if err != nil {
		return nil, err
	}
	return param.send()
}

func newGachaParam(c *handler.Context) (*gachaParam, error) {
	var param gachaParam
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 || param.GoodsID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.uc = c.UserContext()
	param.userID = c.UserID()

	param.r, err = room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if param.r.Limit != nil {
		return nil, actionerrors.NewErrForbidden("本直播间暂不支持本功能")
	}
	if param.r.CreatorID == param.userID {
		return nil, actionerrors.ErrParamsMsg("主播不能在自己直播间购买当前商品")
	}
	if !param.r.IsOpen() {
		return nil, actionerrors.NewErrForbidden("主播休息时暂时无法使用哦~")
	}
	// 被主播拉黑，无法购买
	blocked, err := blocklist.IsBlocked(param.r.CreatorID, param.userID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if blocked {
		return nil, actionerrors.NewErrBlockUser("您当前无法在本直播间内购买当前商品")
	}

	param.goods, err = livegoods.Find(param.GoodsID, livegoods.GoodsTypeGashapon)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.goods == nil {
		return nil, actionerrors.ErrNotFound("无法找到指定商品")
	}
	now := goutil.TimeNow()
	startTime := param.goods.SaleStartTime
	endTime := param.goods.SaleEndTime
	// TODO: 判断是否在商品售卖期间需要封装函数
	if param.goods.Sort <= 0 || now.Unix() < startTime || (endTime != 0 && now.Unix() >= endTime) {
		// 商品不在售卖期间
		return nil, actionerrors.NewErrForbidden("当前时间无法购买当前商品哦~")
	}
	param.more, err = param.goods.UnmarshalMore()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.more == nil {
		return nil, actionerrors.ErrNotFound("无法找到指定商品")
	}
	param.messagePrefix = fmt.Sprintf("通过%s", param.more.GashaponName)
	param.pool, err = gift.FindPoolGashapon(param.more.PoolID, param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.pool == nil {
		return nil, actionerrors.NewErrForbidden("无法购买当前商品")
	}

	param.u, err = liveuser.FindOneSimple(bson.M{"user_id": param.userID}, &liveuser.FindOptions{FindTitles: true, RoomID: param.RoomID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.u == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	_, uv, err := userstatus.UserGeneral(c.UserID(), c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if uv != nil && uv.IsActive() {
		param.uv = uv
	}

	param.bubble, err = userappearance.FindMessageBubble(param.userID)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	param.config, err = params.FindGashapon()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return &param, nil
}

func (param *gachaParam) gacha() error {
	drs, ggID, err := param.pool.Draw(&gift.GashaponDrawParam{
		DrawCount:      param.goods.GoodsNum(),
		RoomID:         param.r.RoomID,
		GuaranteedPool: param.more.GuaranteedPool,
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	giftIDs := make([]int64, len(drs))
	for i := range drs {
		giftIDs[i] = drs[i].GiftID
	}
	gifts, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	for i := range drs {
		drs[i].Gift = gifts[drs[i].GiftID]
		if drs[i].Gift == nil {
			logger.WithFields(logger.Fields{
				"goods_id": param.GoodsID,
				"gift_id":  drs[i].GiftID,
			}).Error("商品配置异常")
			return actionerrors.ErrNotFound("无法找到礼物")
		}
	}
	sort.Slice(drs, func(i, j int) bool {
		return drs[i].Gift.Price > drs[j].Gift.Price
	})
	param.drawResults = drs

	param.lgs = make([]*livegifts.LiveGift, 0, len(drs)) // 礼物插入顺序和抽奖结果相反
	param.orderMore = &livetxnorder.MoreInfo{
		Num:              param.goods.Num,
		GuaranteedGiftID: ggID,
		Gifts:            make([]*livetxnorder.Gift, len(drs)),
	}
	if param.r.IsOpen() {
		param.orderMore.OpenStatus = util.NewInt(livetxnorder.OpenStatusOpen)
	} else {
		param.orderMore.OpenStatus = util.NewInt(livetxnorder.OpenStatusClosed)
	}
	for i := len(drs) - 1; i >= 0; i-- {
		// 礼物消息大奖在后
		lg := livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.u,
			param.bubble).SetGift(drs[i].Gift, int64(drs[i].Num)).
			SetRoomOpenStatus(param.r.IsOpen())
		lg.MessagePrefix = param.messagePrefix
		param.lgs = append(param.lgs, lg)

		param.totalPrice += drs[i].Gift.Price * int64(drs[i].Num)
		// 抽奖信息大奖在前
		param.orderMore.Gifts[i] = &livetxnorder.Gift{
			GiftID: drs[i].GiftID,
			Num:    drs[i].Num,
		}
	}
	return nil
}

func (param *gachaParam) send() (*gachaResp, error) {
	apiGifts := make([]userapi.Gift, len(param.drawResults))
	for i := range apiGifts {
		g := param.drawResults[i].Gift
		apiGifts[i] = userapi.Gift{
			ID:    g.GiftID,
			Title: fmt.Sprintf("%s（%s）", g.Name, param.more.GashaponName),
			Price: g.Price,
			Num:   int64(param.drawResults[i].Num),
		}
	}
	apiGoods := userapi.Goods{
		ID:         param.goods.ID,
		Title:      param.goods.OrderTitle(),
		TotalPrice: param.goods.GoodsTotalPrice(),
		Num:        param.goods.GoodsNum(),
	}
	isNoble := param.uv != nil
	rpcResp, err := userapi.SendGashaponGift(param.userID, param.r.CreatorID,
		apiGoods, apiGifts, isNoble, param.r.Status.OpenLogID, param.uc)
	if err != nil {
		// rpc 接口返回的错误
		return nil, err
	}
	param.balance = rpcResp
	for i := range param.lgs {
		// 存的 tid 是用户消费记录的 tid
		param.lgs[i].SetTransactionIDs(rpcResp.TransactionID)
		param.lgs[i].SetGoodsID(param.goods.ID)
	}
	err = livegifts.SaveMany(param.lgs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// TODO: 构造必要参数
	tl := &transactionlog.TransactionLog{
		ID:     rpcResp.TransactionID,
		FromID: param.userID,
		ToID:   param.r.CreatorID,
		Status: transactionlog.StatusSuccess,
	}
	lto := livetxnorder.NewOrderByTransactionLog(param.goods, tl, param.orderMore)
	err = lto.DB().Create(lto).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	goutil.Go(func() {
		param.addRevenueRank()
		param.addPK()
		param.addMedalPoint()
		param.addUserRedeemPoint()
		param.addUserContribution()
		param.addMultiConnectScore()
		param.addFansBoxEnergy()
		param.notify()

		param.addGashapon()
		param.addActivity()
		param.addLiveShow()
		param.addLiveSpend()
		param.addRoomPaidUser()
	})
	resp := &gachaResp{
		User:          param.u,
		Bubble:        param.bubble,
		Balance:       utils.NewBalanceAfterSend(rpcResp, isNoble),
		MessagePrefix: param.messagePrefix,
		OpenURL:       param.config.OpenURL,
		GashaponNum:   param.goods.Num,
		GrandGiftID:   param.pool.GrandGiftID,
	}
	resp.Gifts = make([]*gift.NotifyGift, len(param.drawResults))
	for i, dr := range param.drawResults {
		resp.Gifts[i] = gift.NewNotifyGift(dr.Gift, dr.Num)
	}
	return resp, nil
}

func (param *gachaParam) addRevenueRank() {
	totalPrice := param.totalPrice
	err := roomsrank.AddRevenue(param.r.RoomID, param.userID, totalPrice, goutil.IntToBool(param.r.Status.Open))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	rankChange, err := usersrank.AddRevenue(param.r.CreatorID, param.r.RoomID, totalPrice)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err := room.NotifyHourRank(rankChange, param.r)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	err = liverevenues.AddGiftRevenue(param.userID, param.r.OID, param.RoomID, totalPrice)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// NOTICE: 目前抽出的礼物数量和 num 总是相等的
	err = param.r.ReceiveGashaponGifts(param.goods.Num, totalPrice)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *gachaParam) addPK() {
	elems, err := livepk.AddPKScore(param.r, param.userID, param.totalPrice, 0)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if len(elems) != 0 {
		param.broadcastElems = append(param.broadcastElems, elems...)
	}
}

func (param *gachaParam) addMedalPoint() {
	if param.r.Medal == nil {
		return
	}
	medalParam := livemedalstats.AddPointParam{
		RoomOID:    param.r.OID,
		RoomID:     param.r.RoomID,
		CreatorID:  param.r.CreatorID,
		UserID:     param.userID,
		UV:         param.uv,
		MedalName:  param.r.Medal.Name,
		Type:       livemedal.TypeGiftAddMedalPoint,
		Source:     livemedal.ChangeSourceGashapon,
		PointAdd:   param.totalPrice,
		Scene:      livemedalpointlog.SceneTypeGashapon,
		IsRoomOpen: param.r.IsOpen(),
	}
	for i := range param.drawResults {
		if param.drawResults[i].Gift.AllowAddMedalPoint() {
			medalParam.PointAdd += param.drawResults[i].Gift.Point *
				int64(param.drawResults[i].Num)
		}
	}
	medalUpdatedInfo, err := medalParam.AddPoint()
	if err != nil {
		logger.Error(err)
		return
	}
	notifyParam := &liveuser.MedalNotifyParam{
		MedalUpdatedInfo: medalUpdatedInfo,
		User:             param.u,
		BubblePtr:        &param.bubble,
		CreatorUsername:  param.r.CreatorUsername,
	}
	notify := notifyParam.NewUserMedalNotify()
	if notify != nil {
		param.broadcastElems = append(param.broadcastElems, notify)
	}
}

func (param *gachaParam) addUserRedeemPoint() {
	err := shop.AddUserRedeemPoint(param.userID, int64(param.goods.Price))
	if err != nil {
		logger.Error(err)
	}
}

func (param *gachaParam) addUserContribution() {
	pointAdd := int64(param.goods.Price * 10) // 1 钻石 = 10 经验
	if param.uv != nil && param.uv.Info != nil {
		pointAdd = param.uv.Info.ScaleContribution(pointAdd)
	}
	addParam := userstatus.NewAddContributionParams(param.userID, param.RoomID, param.r.CreatorUsername, userstatus.FromNormal, param.uv)
	err := addParam.AddPurchaseContribution(pointAdd)
	if err != nil {
		logger.Error(err)
		return
	}
}

func (param *gachaParam) addMultiConnectScore() {
	if !param.r.IsMultiConnect() {
		return
	}

	for i := range param.drawResults {
		elems, err := livemulticonnect.ScoreHelper{
			Room:   param.r,
			Gift:   param.drawResults[i].Gift,
			Num:    int64(param.drawResults[i].Num),
			UserID: param.userID,
		}.AddScore()
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if len(elems) != 0 {
			param.broadcastElems = append(param.broadcastElems, elems...)
		}
	}
}

func (param *gachaParam) addActivity() {
	r := rank.NewSyncParam(param.RoomID, param.userID, param.r.CreatorID).
		SetTransaction(param.balance).
		SetOpenLogID(param.r.Status.OpenLogID).
		SetGuildID(param.r.GuildID).
		SetActivityCatalogID(param.r.ActivityCatalogID).
		SetGoods(param.goods).
		SetGoodsGifts(
			goutil.SliceMap(param.lgs, func(lg *livegifts.LiveGift) *rank.SyncGoodsGifts {
				return &rank.SyncGoodsGifts{
					GiftID:    lg.Gift.GiftID,
					GiftName:  lg.Gift.Name,
					GiftType:  lg.Gift.Type,
					GiftNum:   int(lg.GiftNum),
					GiftPrice: lg.Gift.Price,
					GiftPoint: lg.Gift.Point,
					GiftAttr:  lg.Gift.Attr,
				}
			}),
		)
	r.AddRankPoint()
	r.SendLiveActivity(param.uc, userapi.SyncTypeLuckyGashapon)
}

func (param *gachaParam) addLiveShow() {
	for i := range param.drawResults {
		g := param.drawResults[i].Gift
		liveshow.
			NewSyncLiveShow(param.RoomID, param.u.UserID(), param.r.CreatorID).
			SetGift(g.GiftID, g.Price, param.drawResults[i].Num).
			Sync()
	}
}

func (param *gachaParam) notify() {
	if param.config.EnableNewMsgBroadcast {
		sort.Slice(param.lgs, func(i, j int) bool {
			return param.lgs[i].Gift.Price > param.lgs[j].Gift.Price
		})

		payload := gachaGashaponBroadcastPayload{
			Type:           liveim.TypeGashapon,
			Event:          liveim.EventGashaponGacha,
			RoomID:         param.r.RoomID,
			User:           param.u,
			Time:           goutil.TimeNow().UnixMilli(),
			Bubble:         param.bubble,
			CurrentRevenue: roomsrank.CurrentRevenue(param.r.RoomID, param.u.UserID()),
			Gashapon: &gashaponGachaInfo{
				GoodsID:      param.goods.ID,
				Name:         param.more.GashaponName,
				Contribution: param.goods.Price,
				Num:          param.goods.Num,
				OpenURL:      param.config.OpenURL,
				IconURL:      param.config.IconURL,
				Gifts: goutil.SliceMap(param.lgs,
					func(lg *livegifts.LiveGift) *gashaponGift {
						return &gashaponGift{
							GiftID:       lg.Gift.GiftID,
							GiftName:     lg.Gift.Name,
							Price:        lg.Gift.Price,
							Num:          lg.GiftNum,
							GiftIconURL:  lg.Gift.Icon,
							EffectURL:    lg.Gift.Effect,
							WebEffectURL: lg.Gift.WebEffect,
						}
					},
				),
			},
		}

		// 查询用户已佩戴的送礼通知外观
		giftNotification, err := userappearance.FindWornAppearance(param.userID, appearance.TypeGiftNotification)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		// 添加送礼通知外观
		if giftNotification != nil {
			payload.GiftNotification = userappearance.NewGiftNotification(giftNotification)
		}

		param.broadcastElems = append(param.broadcastElems, &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  param.r.RoomID,
			Payload: payload,
		})
	} else {
		// 房间内消息
		giftNotification, err := userappearance.FindWornAppearance(param.userID, appearance.TypeGiftNotification)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		msgs := make([]*livegifts.RoomMessage, len(param.lgs))
		for i, lg := range param.lgs {
			msgs[i] = lg.RoomMessage()
			msgs[i].OpenURL = param.config.OpenURL
			// 总是有 combo, 以防止连击聚合
			msgs[i].Combo = &livegifts.Combo{
				ID:         lg.OID.Hex(),
				Num:        int(lg.GiftNum),
				RemainTime: 0,
			}
			// WORKAROUND: 安卓 559 iOS 470 之前版本对连击的聚合处理有问题，临时区分礼物 ID
			msgs[i].Gift.GiftID = 10e6 + rand.Int63n(10e6)
			if lg.Gift.ComboFlags(int(lg.GiftNum)).
				IsSet(gift.ComboFlagEffect) {
				msgs[i].Combo.EffectURL = lg.Gift.ComboEffect
				msgs[i].Combo.WebEffectURL = lg.Gift.WebComboEffect
			}

			// 添加送礼通知外观
			if giftNotification != nil {
				msgs[i].GiftNotification = userappearance.NewGiftNotification(giftNotification)
			}
		}
		param.broadcastElems = append(param.broadcastElems, &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  param.r.RoomID,
			Payload: msgs,
		})
	}

	// 全站飘屏
	np := param.pool.NotifyPayload(param.goods.Num,
		param.drawResults, param.u, param.r.RoomID,
		param.r.CreatorUsername, param.more.GashaponName)
	if np != nil {
		param.broadcastElems = append(param.broadcastElems, &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeAll,
			RoomID:  param.RoomID,
			Payload: np,
		})
	}

	if len(param.broadcastElems) != 0 {
		err := userapi.BroadcastMany(param.broadcastElems)
		if err != nil {
			logger.Error(err)
			return
		}
	}
}

func (param *gachaParam) addGashapon() {
	// 增加能量值
	room.GashaponEnergyAdd(param.pool.PoolID, param.r.RoomID, param.goods.GashaponEnergyValue())
	// 主播榜
	err := usersrank.AddGashaponWeek(param.r.RoomID, param.r.CreatorID, param.goods.Num)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	gr := livegashaponuserrank.Rank{
		PoolID: param.pool.PoolID,
		UserID: param.userID,
		Score:  param.totalPrice,
	}
	giftNumMap := make(map[int64]int, len(param.drawResults))
	for i := range param.drawResults {
		// drawResults 在抽奖的实现里面已去重，不需要使用 +=
		giftNumMap[param.drawResults[i].GiftID] = param.drawResults[i].Num
	}
	err = gr.Save(giftNumMap)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	param.addGashaponPrizeList()
}

func (param gachaParam) addGashaponPrizeList() {
	list := make([]string, 0, len(param.drawResults))
	for i, dr := range param.drawResults {
		if dr.Gift.Price <= 10 {
			continue
		}
		prize := gashaponPrize{
			Username:    param.u.Username,
			GashaponNum: param.goods.Num,
			GiftName:    dr.Gift.Name,
			GiftNum:     param.drawResults[i].Num,
			Grand:       param.pool.GrandGiftID == dr.GiftID,
		}
		str, err := json.Marshal(prize)
		if err != nil {
			logger.Error(err)
			continue
			// PASS
		}
		list = append(list, string(str))
	}
	if len(list) == 0 {
		return
	}
	key := keys.KeyGashaponPrizeList0.Format()
	pipe := service.Redis.Pipeline()
	pipe.LPush(key, list)
	pipe.LTrim(key, 0, gashaponPrizeMaxCount-1)
	if _, err := pipe.Exec(); err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *gachaParam) addLiveSpend() {
	utils.SendLiveSpend(param.userID, int64(param.goods.GoodsTotalPrice()), param.RoomID)
}

func (param *gachaParam) addRoomPaidUser() {
	chatroom.AddCurrentRoomPaidUser(param.RoomID, param.userID, param.r.Status.OpenTime)
}

func (param *gachaParam) addFansBoxEnergy() {
	// 超能魔方按实际抽到的礼物价值计算宝箱能量
	contributions := goutil.SliceMap(param.drawResults, func(dr gift.DrawResult) fansboxenergy.GiftContribution {
		return fansboxenergy.GiftContribution{
			Gift: dr.Gift,
			Num:  dr.Num,
		}
	})
	elems := fansboxenergy.AddGiftContributions(param.r, param.userID, contributions)
	param.broadcastElems = append(param.broadcastElems, elems...)
}
