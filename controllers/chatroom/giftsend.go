package chatroom

import (
	"errors"
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/liveshow"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/internal/biz/fansboxenergy"
	"github.com/MiaoSiLa/live-service/models/blackcard/liveblackcardgiftupgrade"
	"github.com/MiaoSiLa/live-service/models/blackcard/liveuserblackcard"
	"github.com/MiaoSiLa/live-service/models/livedb/livegiftupgrade"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalremoverecord"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal/livemedalstats"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liverevenues"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/chatroom"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const sendTypeObtainMedal = 1

// ActionGiftSend 给直播间送礼
/**
 * @api {post} /api/v2/chatroom/gift/send 给直播间送礼
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 实际收礼直播间 ID
 * @apiParam {Number} [from_room_id=0] 用户送礼所在的直播间 ID，不传默认用户在实际收礼的直播间内送礼
 * @apiParam {Number} gift_id 礼物 ID
 * @apiParam {Number} gift_num 礼物数量
 * @apiParam {number=0,1} [combo=0] 是否是连击中的送礼（非首次送礼）
 * @apiParam {number=0,1} [type=0] 送礼方式, 0: 普通送礼，1: 一键获取粉丝勋章（接口会返回粉丝勋章信息）
 *
 * @apiSuccessExample {json} Success-Response
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "user": {
 *           "user_id": 10,
 *           "username": "bless",
 *           "iconurl": "https://static-test.maoercdn.com/avatar/icon01.png",
 *           "titles": [{
 *             "type": "level",
 *             "level": 9
 *           }, {
 *             "type": "medal",
 *             "name": "独角兽",
 *             "level": 4
 *           }, {
 *             "type": "identity_badge", // 身份铭牌
 *             "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *             "appearance_id": 10001 // 图标 ID
 *           }]
 *         },
 *         "room": { // 仅购买一键获取粉丝勋章礼物时返回
 *           "room_id": 1234,
 *           "creator_id": 123456,
 *           "creator_iconurl": "http://static-test.maoercdn.com/avatars/icon01.png",
 *           "creator_username": "主播昵称"
 *         },
 *         "medal": { // 当前直播间勋章，仅购买一键获取粉丝勋章礼物时返回
 *           "name": "只因",
 *           "level": 5,
 *           "frame_url": "http://test.png",
 *         },
 *         "bubble": { // 如果没有特殊气泡，这个字段为 null 或不存在
 *           "type": "message", // 气泡类型，聊天气泡 message
 *           "image_url": "https://static-test.maoercdn.com/live/bubble/image/001.png",
 *           "frame_url": "https://static-test.maoercdn.com/live/bubble/frame/001.png",
 *           "text_color": "#F0F0F0"
 *         },
 *         "balance": {
 *           "balance": 11479968,
 *           "live_noble_balance": 283748,
 *           "live_noble_balance_status": 1
 *         },
 *         "combo": { // 非连击礼物，没有该结构，该字段中特效和 multi_combo 中的特效同时存在时，优先使用 multi_combo 中的特效
 *           "id": "5ef9c9002b9aed35f1607aa2", // 单人连击 ID, 一次一起送连击中可能会发生一个用户的多次单人连击
 *           "num": 2, // 当前用户的本轮单人连击礼物数量
 *           "effect_url": "https://static-test.maoercdn.com/gifts/comboeffects/101.svga", // 需要展示单人连击特效时会下发（兼容旧版本，一起送特效也会这里同时下发）
 *           "web_effect_url": "https://static-test.maoercdn.com/gifts/comboeffects/101.svga", // 需要展示单人连击特效时会下发（兼容旧版本，一起送特效也会这里同时下发）
 *           "remain_time": 5000 // 连击剩余时间 (ms)
 *         },
 *         "multi_combo": { // 一起送连击礼物进度
 *           "id": "6ef9c9002b9aed35f1607aa3",
 *           "num": 180, // 当前用户的一起送连击礼物数量
 *           "effect_url": "https://static-test.maoercdn.com/gifts/comboeffects/101.svga", // 需要展示一起送连击特效时会下发
 *           "web_effect_url": "https://static-test.maoercdn.com/gifts/comboeffects/101.svga", // 需要展示一起送连击特效时会下发
 *           "total_num": 150, // 礼物连击的总数量，客户端根据送礼总金额（总数*礼物单价）和倒计时维护需要展示的信息，仅直播间连击礼物下发
 *           "achieved_num": 0, // 计算进度的档位起始数量，仅直播间连击礼物下发，直播间连击计算进度的公式: (total_num - achieved_num) / (target_num - achieved_num)
 *           "target_num": 200, // 当前档位触发连击特效需要的总累计礼物数量，仅直播间连击礼物下发
 *           "remain_time": 15000 // 连击剩余时间 (ms)
 *         },
 *         "upgrade": { // 升级礼物信息，集齐礼物后不下发
 *           "discount": { // 折扣信息，没有折扣时不下发
 *             "label_icon_url": "https://static-test.maoercdn.com/gifts/labels/001.png",
 *           },
 *           "upgrade_num": {
 *             "remain_upgrade_num": 1, // 剩余可升级次数
 *             "upgrade_gift_num": 12, // 本次升级所需礼物数量
 *             "gift_num": 10 // 本次升级已累积赠送礼物数量
 *           }
 *         }
 *       }
 *     }
 *
 * @apiSuccessExample {json} WebSocket 收礼直播间内消息
 *     {
 *       "type": "gift",
 *       "event": "send",
 *       "room_id": 65261414,
 *       "user": {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.maoercdn.com/avatar/icon01.png",
 *         "titles": [{
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#f45b41"
 *         }, {
 *           "type": "level",
 *           "level": 9
 *         }, {
 *           "type": "medal",
 *           "name": "独角兽",
 *           "level": 4
 *         }, {
 *           "type": "noble",
 *           "name": "新秀",
 *           "level": 2
 *         }, {
 *           "type": "avatar_frame",
 *           "icon_url": "https://static-test.maoercdn.com/gifts/avatarframes/002.png"
 *         }, {
 *           "type": "identity_badge", // 身份铭牌
 *           "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *           "appearance_id": 10001 // 图标 ID
 *         }]
 *       },
 *       "time": 1576744741101,
 *       "gift": {
 *         "gift_id": 1,
 *         "name": "药丸",
 *         "icon_url": "https://static-test.maoercdn.com/gifts/icons/001.png",
 *         // 如果下发的消息没有 effect_url，需要支持从 meta/data 存储的数据中查询出对应的 effect_url 和 effect_duration
 *         "effect_url": "https://static-test.maoercdn.com/gifts/effects/001.mp4", // 礼物特效（有特效的礼物才有这个字段），客户端需要支持 mp4、svga 和 png 格式
 *                                                                                 // 礼物特效地址支持使用 `;` 分割
 *         "effect_duration": 5000, // 特效时长，如果有就使用。对于无法计算出时长的特效且无法获取出 effect_duration (包括没有在 meta/data) 数据的，使用默认时长 5000ms
 *         "price": 6,
 *         "num": 1
 *       },
 *       "gift_notification": { // 用户佩戴的送礼通知皮肤，未佩戴送礼通知皮肤时不下发，客户端和前端使用默认送礼通知皮肤
 *         "username_color": "#F0F0F0",
 *         "text_color": "#F0F0F0",
 *         "frame_url": "https://static-test.maoercdn.com/live/gift/notification/blackcard-1.png"
 *       },
 *       "bubble": { // 如果没有特殊气泡，这个字段为 null 或不存在
 *         "type": "message", // 气泡类型，聊天气泡 message
 *         "image_url": "https://static-test.maoercdn.com/live/bubble/image/001.png",
 *         "frame_url": "https://static-test.maoercdn.com/live/bubble/frame/001.png",
 *         "text_color": "#F0F0F0"
 *       },
 *       "combo": { // 可能为空，如果存在显示的礼物数量优先使用 combo 中的 num
 *         "id": "5ef9c9002b9aed35f1607aa2",
 *         "num": 100, // 当前用户的本轮单人连击礼物数量
 *         "effect_url": "https://static-test.maoercdn.com/gifts/comboeffects/101.svga", // 需要展示单人连击特效时会下发
 *         "web_effect_url": "https://static-test.maoercdn.com/gifts/comboeffects/101.svga", // 需要展示单人连击特效时会下发
 *         "remain_time": 5000 // 单人连击剩余时间 (ms)
 *       },
 *       "multi_combo": { // 一起送连击礼物进度
 *         "id": "6ef9c9002b9aed35f1607aa3",
 *         "num": 2, // 当前用户的一起送连击礼物数量
 *         "effect_url": "https://static-test.maoercdn.com/gifts/comboeffects/101.svga", // 需要展示一起送特效时会下发
 *         "web_effect_url": "https://static-test.maoercdn.com/gifts/comboeffects/101.svga", // 需要展示一起送特效时会下发
 *         "total_num": 150, // 礼物连击的总数量，客户端根据送礼总金额（总数*礼物单价）和倒计时维护需要展示的信息
 *         "achieved_num": 0, // 计算进度的档位起始数量，直播间连击计算进度的公式: (total_num - achieved_num) / (target_num - achieved_num)
 *         "target_num": 200, // 当前档位触发连击特效需要的总累计礼物数量
 *         "remain_time": 15000 // 一起送连击剩余时间 (ms)
 *       },
 *       "current_revenue": 612 // 当前用户在当前直播间本场榜的贡献值
 *     }
 *
 * @apiSuccessExample {json} WebSocket 送礼用户消息，仅当用户在非收礼直播间内送礼时下发，仅送礼用户自己可见
 *     // 因为不需要展示到公屏，这里没有 bubble 和 effect 等字段
 *     {
 *       "type": "gift",
 *       "event": "cross_send", // 非收礼直播间内送礼通知使用，不需要累加当前直播间的收益和更新小时榜排名
 *       "room_id": 65261414, // 用户送礼时所在的直播间
 *       "user": {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.maoercdn.com/avatar/icon01.png"
 *       },
 *       "room": { // 收礼直播间信息
 *         "room_id": 1234,
 *         "creator_id": 123456,
 *         "creator_iconurl": "http://static-test.maoercdn.com/avatars/icon01.png",
 *         "creator_username": "主播昵称"
 *       },
 *       "time": 1576744741101, // 毫秒时间戳
 *       "gift": {
 *         "gift_id": 1,
 *         "name": "药丸",
 *         "icon_url": "https://static-test.maoercdn.com/gifts/icons/001.png",
 *         "price": 6, // 单位：钻石
 *         "num": 1
 *       },
 *       "gift_notification": { // 用户佩戴的送礼通知皮肤，未佩戴送礼通知皮肤时不下发，客户端和前端使用默认送礼通知皮肤
 *         "username_color": "#F0F0F0",
 *         "text_color": "#F0F0F0",
 *         "frame_url": "https://static-test.maoercdn.com/live/gift/notification/blackcard-1.png"
 *       },
 *       "combo": { // 可能为空，如果存在显示的礼物数量优先使用 combo 中的 num
 *         "id": "5ef9c9002b9aed35f1607aa2",
 *         "num": 100, // 当前用户的本轮单人连击礼物数量
 *         "remain_time": 5000 // 单人连击剩余时间 (ms)
 *       },
 *       "multi_combo": { // 一起送连击礼物进度
 *         "id": "6ef9c9002b9aed35f1607aa3",
 *         "num": 2, // 当前用户的一起送连击礼物数量
 *         "remain_time": 15000 // 一起送连击剩余时间 (ms)
 *       }
 *     }
 *
 * @apiSuccessExample {json} 粉丝勋章升级
 *     {
 *       "type": "user_notify",
 *       "notify_type": "medal",
 *       "event": "update",
 *       "room_id": 179257943,
 *       "user_id": 10,
 *       "medal": {
 *         "name": "呼吸过度",
 *         "level": 5
 *       }
 *     }
 *
 * @apiSuccessExample {json} 全站飘屏
 *     {
 *       "type": "notify",
 *       "notify_type": "gift",
 *       "event": "send",
 *       "room_id": 65261414,
 *       "user": {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.maoercdn.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *         "titles": [{
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#f45b41"
 *         }, {
 *           "type": "level",
 *           "level": 9
 *         }, {
 *           "type": "avatar_frame",
 *           "icon_url": "https://static-test.maoercdn.com/gifts/avatarframes/002.png"
 *         }, {
 *           "type": "identity_badge", // 身份铭牌
 *           "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *           "appearance_id": 10001 // 图标 ID
 *         }]
 *       },
 *       "gift": {
 *         "gift_id": 11,
 *         "name": "更贵的城堡",
 *         "icon_url": "https://static-test.maoercdn.com/gifts/icons/010.png",
 *         "price": 20000,
 *         "num": 1,
 *         "effect_url": "https://static-test.maoercdn.com/gifts/effects/010-mobile.svga"
 *       },
 *       "bubble": { // 用户消息气泡，如果没有气泡，不下发该字段
 *         "type": "message", // 气泡类型，聊天气泡 message
 *         "image_url": "https://static-test.maoercdn.com/live/bubble/image/001.png",
 *         "frame_url": "https://static-test.maoercdn.com/live/bubble/frame/001.png",
 *         "text_color": "#F0F0F0"
 *       },
 *       "notify_bubble": { // 飘屏气泡
 *         "type": "custom",
 *         "bubble_id": 90106,
 *         "image_url": "https://static-test.maoercdn.com/live/bubbles/notify/b90106_0_132_0_180.png"
 *       },
 *       "combo": { // 可能为空，如果存在显示的礼物数量优先使用 combo 中的 num
 *         "id": "5ef9c9002b9aed35f1607aa2",
 *         "num": 2, // 当前用户的本轮单人连击礼物数量
 *         "effect_url": "https://static-test.maoercdn.com/gifts/comboeffects/101.svga", // 需要展示单人特效时会下发
 *         "web_effect_url": "https://static-test.maoercdn.com/gifts/comboeffects/101.svga", // 需要展示单人特效时会下发
 *         "remain_time": 5000 // 单人连击剩余时间 (ms)
 *       },
 *       "multi_combo": { // 一起送连击礼物进度
 *         "id": "6ef9c9002b9aed35f1607aa3",
 *         "num": 180, // 当前用户的一起送连击礼物数量
 *         "effect_url": "https://static-test.maoercdn.com/gifts/comboeffects/101.svga", // 需要展示一起送特效时会下发
 *         "web_effect_url": "https://static-test.maoercdn.com/gifts/comboeffects/101.svga", // 需要展示一起送特效时会下发
 *         "total_num": 150, // 礼物连击的总数量，客户端根据送礼总金额（总数*礼物单价）和倒计时维护需要展示的信息
 *         "achieved_num": 0, // 计算进度的档位起始数量，直播间连击计算进度的公式: (total_num - achieved_num) / (target_num - achieved_num)
 *         "target_num": 200, // 当前档位触发连击特效需要的总累计礼物数量
 *         "remain_time": 15000 // 一起送连击剩余时间 (ms)
 *       },
 *       "message": "<b>bless</b> 给 <b>绵绵思远道い</b> 送出 <b>1 个更贵的城堡</b>，快来围观吧~"
 *     }
 *
 */
func ActionGiftSend(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGiftSendParam(c)
	if err != nil {
		return nil, err
	}
	return param.send()
}

type roomCreator struct {
	RoomID          int64  `json:"room_id"`
	CreatorID       int64  `json:"creator_id"`
	CreatorUsername string `json:"creator_username"`
	CreatorIconURL  string `json:"creator_iconurl"`
}

type sendResp struct {
	User       *liveuser.Simple             `json:"user"`
	Room       *roomCreator                 `json:"room,omitempty"`
	Medal      *livemedal.Mini              `json:"medal,omitempty"`
	Bubble     *bubble.Simple               `json:"bubble,omitempty"`
	Balance    *utils.BalanceAfterSend      `json:"balance"`
	Combo      *livegifts.Combo             `json:"combo,omitempty"`
	MultiCombo *livegifts.Combo             `json:"multi_combo,omitempty"`
	Upgrade    *livegiftupgrade.UpgradeInfo `json:"upgrade,omitempty"`
}

type giftSendParam struct {
	RoomID     int64 `form:"room_id" json:"room_id"`
	FromRoomID int64 `form:"from_room_id" json:"from_room_id"`
	GiftID     int64 `form:"gift_id" json:"gift_id"`
	GiftNum    int   `form:"gift_num" json:"gift_num"`
	Combo      int   `form:"combo" json:"combo"`
	SendType   int   `form:"type" json:"type"`

	uc      mrpc.UserContext
	userCtx userapi.UserContext
	equip   *goutil.Equipment

	r        *room.Room
	fromRoom *room.Room

	user      *liveuser.Simple
	uv        *vip.UserVip
	bubble    *bubble.Simple
	roomMedal *livemedal.LiveMedal // 当前房间的粉丝勋章

	// TODO: 查询连击相关
	g *gift.Gift

	// 基础升级礼物
	baseUpgradeGift *gift.Gift

	lg             *livegifts.LiveGift
	combo          *livegifts.Combo
	multiCombo     *livegifts.Combo
	balance        *userapi.BalanceResp // 交易响应信息
	broadcastElems []*userapi.BroadcastElem
}

func newGiftSendParam(c *handler.Context) (*giftSendParam, error) {
	var param giftSendParam
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 ||
		param.GiftID <= 0 || param.GiftNum <= 0 ||
		param.SendType < 0 || param.SendType > sendTypeObtainMedal ||
		param.FromRoomID < 0 || param.FromRoomID == param.RoomID {
		return nil, actionerrors.ErrParams
	}

	param.uc = c.UserContext()
	param.userCtx = userapi.NewUserContext(c)
	param.equip = c.Equip()

	param.r, err = room.Find(param.RoomID, &room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if param.FromRoomID > 0 {
		param.fromRoom, err = room.Find(param.FromRoomID, &room.FindOptions{DisableAll: true})
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if param.fromRoom == nil {
			return nil, actionerrors.ErrCannotFindRoom
		}
	}
	userID := c.UserID()
	if param.r.CreatorID == userID {
		return nil, actionerrors.ErrParamsMsg("无法给自己的直播间送礼物")
	}
	// NOTICE: 猫耳娘的零钱袋是活动发奖账号，不受黑名单的限制
	if userID != userstatus.MaoerWalletUserID {
		// 被主播拉黑无法送礼
		blocked, err := blocklist.IsBlocked(param.r.CreatorID, userID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if blocked {
			return nil, actionerrors.NewErrBlockUser("您当前无法在本直播间内进行此操作")
		}
	}
	// 直播间没有勋章时，不支持一键送礼获取勋章
	if param.SendType == sendTypeObtainMedal {
		if param.r.Medal == nil {
			return nil, actionerrors.NewErrForbidden("当前直播间未拥有勋章")
		}
		if param.r.Limit != nil {
			// 礼物房不支持一键获取礼物勋章
			return nil, actionerrors.NewErrForbidden("本直播间暂不支持本功能")
		}
	}

	// 判断是不是礼物房
	if param.r.Limit != nil && param.r.Limit.Type == room.LimitTypeNormalGift &&
		!goutil.HasElem(param.r.Limit.AllowedUserIDs, userID) {
		return nil, actionerrors.NewErrForbidden("本直播间内无法赠送该礼物")
	}
	// 判断是否允许跨直播送礼
	if err = checkAllowCrossSend(param.FromRoomID, param.RoomID); err != nil {
		return nil, err
	}

	param.user, err = liveuser.FindOneSimple(bson.M{"user_id": userID},
		&liveuser.FindOptions{FindTitles: true, RoomID: param.RoomID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.user == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	uv, err := vip.UserActivatedVip(userID, false, c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if uv != nil && uv.IsActive() {
		param.uv = uv
	}
	param.bubble, err = userappearance.FindMessageBubble(userID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 查询当前房间粉丝勋章
	if param.r.Medal != nil {
		param.roomMedal, err = livemedal.FindOwnedMedal(userID,
			param.r.RoomID, livemedal.FindOptions{OnlyMedal: true},
		)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	err = param.findGift()
	if err != nil {
		return nil, err
	}
	return &param, nil
}

func checkAllowCrossSend(fromRoomID, toRoomID int64) error {
	if fromRoomID == 0 {
		return nil
	}
	members, err := livemulticonnect.FindOngoingMembers([]int64{fromRoomID, toRoomID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(members) != 2 {
		return actionerrors.NewErrForbidden("直播间未在主播连线中，无法赠送该礼物")
	}
	if members[0].GroupID != members[1].GroupID {
		return actionerrors.NewErrForbidden("对方直播间未在连线组中，无法赠送该礼物")
	}
	return nil
}

func findGift(giftID int64, roomID int64, user *liveuser.Simple, uv *vip.UserVip, roomMedal *livemedal.LiveMedal) (*gift.Gift, error) {
	g, err := gift.FindShowingGiftByGiftID(giftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil || !gift.AbleToSend(g.Type) {
		return nil, actionerrors.ErrNotFound("无法找到指定礼物")
	}

	// 检查赠送资格
	// 贵族资格，优先判断用户定制礼物的贵族要求
	if !g.OwnedByVIP(uv) {
		return nil, actionerrors.NewErrForbidden("无法购买当前贵族礼物")
	}
	// 用户定制礼物
	if !g.OwnedByUser(user.UserID(), user.Level()) {
		return nil, actionerrors.ErrNotFound("无法找到指定礼物")
	}
	// 房间定制礼物
	if !g.OwnedByRoom(roomID) {
		return nil, actionerrors.ErrNotFound("无法找到指定礼物")
	}
	// 粉丝礼物
	if !g.OwnedByFans(roomMedal) {
		return nil, actionerrors.NewErrForbidden("无法购买当前粉丝礼物")
	}

	return g, nil
}

func (param *giftSendParam) findGift() (err error) {
	if param.SendType == sendTypeObtainMedal {
		// 一键购买粉丝勋章一次只能买 1 个
		if param.GiftNum != 1 {
			return actionerrors.NewErrForbidden("无法购买当前礼物")
		}
		switch param.GiftID {
		case livemedal.ObtainMedalGiftID:
			// PASS
		case livemedal.ObtainMedalHalfPriceGiftID:
			pointMulti, _ := livemedal.FindMedalPointMultiple(param.r.CreatorID, false)
			if pointMulti <= 1 {
				return actionerrors.NewErrForbidden("无法购买当前礼物")
			}
		case livemedal.ObtainMedalDiscountGiftID:
			// 如果已有房间的粉丝勋章，则无法购买折扣粉丝勋章
			if param.roomMedal != nil {
				return actionerrors.NewErrForbidden("无法购买当前礼物")
			}
			medalCfg, err := params.FindMedal()
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}
			if !medalCfg.IsFirstMedalDiscountEnable(param.equip) {
				return actionerrors.NewErrForbidden("无法购买当前礼物")
			}
			exists, err := livemedalremoverecord.IsUserEverHadMedal(param.user.UserID())
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}
			if exists {
				return actionerrors.NewErrForbidden("无法购买当前礼物")
			}
		default:
			return actionerrors.NewErrForbidden("无法购买当前礼物")
		}
		// 指定礼物即使被下架，一键获取粉丝勋章也需要能正常赠送
		param.g, err = gift.FindByGiftID(param.GiftID)
		return err
	}
	param.g, err = findGift(param.GiftID, param.r.RoomID, param.user, param.uv, param.roomMedal)
	if err != nil {
		return err
	}
	if param.g.Comboable == gift.ComboableTypeMulti {
		comboParam, err := params.FindMultiCombo()
		if err != nil {
			logger.WithField("room_id", param.r.RoomID).Error(err)
			// PASS
		}
		if !comboParam.EnableSend(param.r.RoomID) {
			return actionerrors.NewErrForbidden("无法购买当前礼物")
		}
	}
	// 升级礼物
	if param.g.Type == gift.TypeUpgrade {
		upgradeGift, err := livegiftupgrade.FindByUpgradeGiftID(param.g.GiftID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if upgradeGift == nil {
			return actionerrors.NewErrForbidden("无法购买当前礼物")
		}
		param.baseUpgradeGift, err = gift.FindShowingGiftByGiftID(upgradeGift.BaseGiftID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if param.baseUpgradeGift == nil {
			return actionerrors.NewErrForbidden("无法购买当前礼物")
		}
		unlocked, err := livegiftupgrade.IsUnlockUpgradeGift(param.user.UserID(), upgradeGift.BaseGiftID, param.g.ToggleTime, param.g.GiftID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if !unlocked {
			return actionerrors.NewErrForbidden("无法购买当前礼物")
		}
	}
	if param.g.IsUpgradeBaseGift() {
		param.baseUpgradeGift = param.g
	}

	// 黑卡钻石皮肤礼物
	if param.g.Type == gift.TypeBlackCard {
		userBlackCard, err := liveuserblackcard.FindUserActiveBlackCard(param.user.UserID())
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if userBlackCard == nil {
			return actionerrors.NewErrForbidden("无法购买当前礼物")
		}

		blackCardGiftUpgrade, err := liveblackcardgiftupgrade.FindBlackCardGiftUpgrade(param.g.GiftID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if blackCardGiftUpgrade == nil || userBlackCard.Level < blackCardGiftUpgrade.Level {
			return actionerrors.NewErrForbidden("无法购买当前礼物")
		}
	}

	return nil
}

func (param *giftSendParam) send() (*sendResp, error) {
	apiResp, err := userapi.SendGift(param.user.UserID(),
		param.r.CreatorID, userapi.Gift{
			ID:    param.g.GiftID,
			Title: param.g.Name,
			Price: param.g.Price,
			Num:   int64(param.GiftNum),
		}, param.uv != nil, param.r.Status.OpenLogID, param.userCtx)
	if err != nil {
		return nil, err
	}
	param.balance = apiResp
	param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID,
		param.user, param.bubble).SetGift(param.g, int64(param.GiftNum)).
		SetRoomOpenStatus(param.r.IsOpen()).SetTransactionIDs(apiResp.TransactionID)
	err = param.saveLiveGifts()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	resp := &sendResp{
		User:   param.user,
		Bubble: param.bubble,
		Balance: &utils.BalanceAfterSend{
			Balance:                apiResp.Balance,
			LiveNobleBalance:       apiResp.LiveNobleBalance,
			LiveNobleBalanceStatus: util.BoolToInt(param.uv != nil),
		},
		Combo:      param.combo,
		MultiCombo: param.multiCombo,
	}
	if param.SendType == sendTypeObtainMedal {
		// 仅一键获取粉丝勋章需要返回升级后的勋章信息
		param.addMedalPoint()
	}
	goutil.Go(func() {
		// 礼物房不需要处理送礼各榜单积分及亲密度，只发送直播间内送礼消息，故将礼物房的处理放在 goroutine 各方法中。
		param.addRevenueRank()
		param.addPK()
		if param.SendType != sendTypeObtainMedal {
			// 普通送礼的亲密度处理放在 goroutine 中处理
			param.addMedalPoint()
		}
		param.addMultiConnectScore()
		param.addUserContribution()
		param.activatedGiftWall()
		param.addFansBoxEnergy()
		param.buildIMMessage()
		param.broadcast()

		param.addActivity()
		param.addLiveShow()
		param.addLiveSpend()
		param.addRoomPaidUser()
	})
	if param.SendType == sendTypeObtainMedal {
		resp.Room = &roomCreator{
			RoomID:          param.r.RoomID,
			CreatorID:       param.r.CreatorID,
			CreatorUsername: param.r.CreatorUsername,
			CreatorIconURL:  param.r.CreatorIconURL,
		}
		if param.roomMedal != nil {
			resp.Medal = &param.roomMedal.Mini
		}
	}
	resp.Upgrade = param.buildUpgradeGift()
	return resp, nil
}

func (param *giftSendParam) saveLiveGifts() error {
	if param.g.Comboable == gift.ComboableTypeSingle || // 单人连击礼物
		param.g.Comboable == gift.ComboableTypeMulti { // 直播间一起送连击礼物
		return param.saveCombo()
	}
	var err error
	// 非连击礼物直接保存
	param.lg.OID, err = livegifts.UpdateSave(param.lg, nil, primitive.NilObjectID)
	if err != nil {
		return err
	}
	return nil
}

func (param *giftSendParam) saveCombo() error {
	loggerFields := logger.Fields{
		"room_id": param.r.RoomID,
		"user_id": param.user.UserID(),
		"gift_id": param.g.GiftID,
	}
	combo := param.newComboer()
	if combo == nil {
		return nil
	}
	retryCount := combo.retryCount()
	for i := 0; i < retryCount; i++ {
		ok, err := combo.lock()
		if err != nil || !ok {
			if err != nil {
				logger.WithFields(loggerFields).Error(err)
			}
			if i < retryCount-1 {
				time.Sleep(50 * time.Millisecond) // retry in next 50ms
				continue
			} else {
				logger.WithFields(loggerFields).Error("Get gift combo lock timeout.")
				break
			}
		}
		err = param.updateLiveGift(combo)
		if err != nil {
			unlockErr := combo.unlock() // 没有 err 时，是在更新连击缓存信息时进行解锁操作
			if unlockErr != nil {
				logger.WithFields(loggerFields).Error(unlockErr)
				// PASS
			}
			return err
		}
		param.lg = param.lg.SetCombo(param.combo).SetMultiCombo(param.multiCombo)
		return nil
	}
	return nil
}

// 记录送礼信息并更新连击信息
func (param *giftSendParam) updateLiveGift(combo comboer) error {
	if param.g.Comboable == gift.ComboableTypeMulti || param.Combo != 0 { // 单人连击礼物连击状态要优先用客户端维护的状态
		ok, err := combo.loadCache(param)
		if err != nil {
			return err
		}
		if ok {
			// 连击中
			err = combo.updateCombo(param)
			if err != nil {
				return err
			}
			return nil
		}
	}
	// 新建连击
	err := combo.saveCombo(param)
	if err != nil {
		return err
	}
	return nil
}

// 连击 interface
type comboer interface {
	retryCount() int
	lock() (bool, error)
	unlock() error // 插入更新 live_gift 没有 err 时，是在更新连击缓存信息的同时进行解锁操作
	loadCache(param *giftSendParam) (bool, error)
	saveCombo(param *giftSendParam) error
	updateCombo(param *giftSendParam) error
}

func (param *giftSendParam) newComboer() comboer {
	var (
		fieldComboLock     = keys.FieldComboLock0.Format()
		fieldComboID       = keys.FieldComboID0.Format()
		fieldComboGiftNum  = keys.FieldComboGiftNum0.Format()
		fieldComboLastTime = keys.FieldComboLastTimeMilli0.Format()
	)

	switch param.g.Comboable {
	case gift.ComboableTypeSingle:
		return &singleComboInfo{
			key:                keys.KeyRoomUserGiftCombo3.Format(param.r.RoomID, param.user.UserID(), param.g.GiftID),
			fieldComboLock:     fieldComboLock,
			fieldComboID:       fieldComboID,
			fieldComboGiftNum:  fieldComboGiftNum,
			fieldComboLastTime: fieldComboLastTime,
		}
	case gift.ComboableTypeMulti:
		return &multiComboInfo{
			mc: &singleComboInfo{
				key:                keys.KeyRoomGiftMultiCombo2.Format(param.r.RoomID, param.g.GiftID),
				fieldComboLock:     fieldComboLock,
				fieldComboID:       fieldComboID,
				fieldComboGiftNum:  fieldComboGiftNum,
				fieldComboLastTime: fieldComboLastTime,
			},
			sc: &singleComboInfo{
				key:                keys.KeyRoomUserGiftCombo3.Format(param.r.RoomID, param.user.UserID(), param.g.GiftID),
				fieldComboLock:     fieldComboLock,
				fieldComboID:       fieldComboID,
				fieldComboGiftNum:  fieldComboGiftNum,
				fieldComboLastTime: fieldComboLastTime,
			},
			userID: param.user.UserID(),
		}
	}
	return nil
}

type singleComboInfo struct {
	key                string
	fieldComboLock     string
	fieldComboID       string
	fieldComboGiftNum  string
	fieldComboLastTime string

	comboID           string
	giftNum           int64
	lastTimeMilliUnix int64
	previousGiftNum   int64
}

func (sc *singleComboInfo) retryCount() int {
	return 5
}

func (sc *singleComboInfo) lock() (bool, error) {
	pipe := service.Redis.TxPipeline()
	cmd := pipe.HSetNX(sc.key, sc.fieldComboLock, 1)
	pipe.Expire(sc.key, 40*time.Second)
	_, err := pipe.Exec()
	if err != nil {
		return false, err
	}
	return cmd.Val(), nil
}

func (sc *singleComboInfo) unlock() error {
	return service.Redis.HDel(sc.key, sc.fieldComboLock).Err()
}

func (c *comboCache) isInCombo(allowDuration time.Duration, price int64) bool {
	lastTime := goutil.TimeUnixMilli(c.lastTimeMilliUnix).ToTime()
	remainDuration := gift.ComboRemainDuration(c.giftNum * price)
	// 允许误差时长 allowDuration
	allowComboTime := lastTime.Add(remainDuration).Add(allowDuration)
	return goutil.TimeNow().Before(allowComboTime)
}

type comboCache struct {
	comboID           string
	giftNum           int64
	lastTimeMilliUnix int64
}

var errComboGiftUnknown = errors.New("当前礼物连击状态错误")

func findComboCache(key string) (*comboCache, error) {
	var (
		fieldComboID       = keys.FieldComboID0.Format()
		fieldComboGiftNum  = keys.FieldComboGiftNum0.Format()
		fieldComboLastTime = keys.FieldComboLastTimeMilli0.Format()
	)

	res, err := service.Redis.HMGet(key, fieldComboID, fieldComboGiftNum, fieldComboLastTime).Result()
	if err != nil {
		return nil, err
	}
	if res[0] == nil || res[1] == nil || res[2] == nil {
		return nil, nil
	}
	var ok bool
	combo := new(comboCache)
	combo.comboID, ok = res[0].(string)
	if !ok {
		return nil, errComboGiftUnknown
	}
	giftNumStr, ok := res[1].(string)
	if !ok {
		return nil, errComboGiftUnknown
	}
	combo.giftNum, err = strconv.ParseInt(giftNumStr, 10, 64)
	if err != nil {
		return nil, err
	}
	lastTimeStr, ok := res[2].(string)
	if !ok {
		return nil, errComboGiftUnknown
	}
	combo.lastTimeMilliUnix, err = strconv.ParseInt(lastTimeStr, 10, 64)
	if err != nil {
		return nil, err
	}
	return combo, nil
}

func (sc *singleComboInfo) loadCache(param *giftSendParam) (bool, error) {
	combo, err := findComboCache(sc.key)
	if err != nil {
		return false, err
	}
	if combo == nil {
		return false, nil
	}
	// 实际是否连击以客户端传值 combo 为准，若客户端认为连击中，这里相比规则，允许误差时长 5s
	if !combo.isInCombo(5*time.Second, param.g.Price) {
		return false, nil
	}
	sc.comboID = combo.comboID
	sc.giftNum = combo.giftNum
	sc.lastTimeMilliUnix = combo.lastTimeMilliUnix
	return true, nil
}

func (sc *singleComboInfo) saveCombo(param *giftSendParam) error {
	remainTime := goutil.TimeNow().Add(gift.ComboRemainDuration(int64(param.GiftNum) * param.g.Price))
	var err error
	param.lg.OID, err = livegifts.UpdateSave(param.lg, &remainTime, primitive.NilObjectID)
	if err != nil {
		return err
	}
	sc.comboID = param.lg.OID.Hex()
	sc.giftNum = int64(param.GiftNum)
	pipe := service.Redis.TxPipeline()
	pipe.HMSet(sc.key, map[string]interface{}{
		sc.fieldComboID:       sc.comboID,
		sc.fieldComboGiftNum:  sc.giftNum,
		sc.fieldComboLastTime: goutil.TimeNow().UnixMilli(),
	})
	pipe.HDel(sc.key, sc.fieldComboLock)
	pipe.Expire(sc.key, 20*time.Second)
	_, err = pipe.Exec()
	if err != nil {
		return err
	}
	param.combo = sc.buildMessageCombo(param)
	return nil
}

func (sc *singleComboInfo) buildMessageCombo(param *giftSendParam) *livegifts.Combo {
	combo := &livegifts.Combo{
		ID:         sc.comboID,
		Num:        int(sc.giftNum),
		RemainTime: gift.ComboRemainDuration(sc.giftNum * param.g.Price).Milliseconds(),
	}
	combo.BuildEffect(param.g, sc.previousGiftNum)
	return combo
}

func (sc *singleComboInfo) updateCombo(param *giftSendParam) error {
	comboOID, err := primitive.ObjectIDFromHex(sc.comboID)
	if err != nil {
		return err
	}
	remainDuration := gift.ComboRemainDuration(sc.giftNum * param.g.Price)
	remainTime := goutil.TimeNow().Add(remainDuration)
	param.lg.OID, err = livegifts.UpdateSave(param.lg, &remainTime, comboOID)
	if err != nil {
		return err
	}
	pipe := service.Redis.TxPipeline()
	numCmd := pipe.HIncrBy(sc.key, sc.fieldComboGiftNum, int64(param.GiftNum))
	pipe.HSet(sc.key, sc.fieldComboLastTime, goutil.TimeNow().UnixMilli())
	pipe.HDel(sc.key, sc.fieldComboLock)
	pipe.Expire(sc.key, 20*time.Second)
	_, err = pipe.Exec()
	if err != nil {
		return err
	}
	sc.giftNum = numCmd.Val()
	sc.previousGiftNum = sc.giftNum - int64(param.GiftNum)
	param.combo = sc.buildMessageCombo(param)
	return nil
}

type multiComboInfo struct {
	mc *singleComboInfo
	sc *singleComboInfo // 一起送也需要记录单人连击信息，通过一起送 mc.key 的锁来实现统一控制

	userID      int64 // 当前送礼用户
	userRankNum int64 // 当前送礼用户在本次连击中赠送的礼物数量

	top1UserID int64
}

func (mc *multiComboInfo) retryCount() int {
	return 10
}

func (mc *multiComboInfo) lock() (bool, error) {
	return mc.mc.lock()
}

func (mc *multiComboInfo) unlock() error {
	return mc.mc.unlock()
}

func (mc *multiComboInfo) loadCache(param *giftSendParam) (bool, error) {
	combo, err := findComboCache(mc.mc.key)
	if err != nil {
		return false, err
	}
	if combo == nil || !combo.isInCombo(1*time.Second, param.g.Price) {
		// 一起送连击不存在的时候，一起送连击和单人连击都会一起创建新的
		return false, nil
	}
	mc.mc.comboID = combo.comboID
	mc.mc.giftNum = combo.giftNum
	mc.mc.lastTimeMilliUnix = combo.lastTimeMilliUnix
	singleCombo, err := findComboCache(mc.sc.key)
	if err != nil {
		logger.WithField("key", mc.sc.key).Error(err)
		// PASS
	}
	// 一起送中可能会包含用户多次单人连击，单人连击不存在时会创建新的
	if singleCombo != nil && singleCombo.isInCombo(1*time.Second, param.g.Price) {
		mc.sc.comboID = singleCombo.comboID
		mc.sc.giftNum = singleCombo.giftNum
		mc.sc.lastTimeMilliUnix = singleCombo.lastTimeMilliUnix
	}
	return true, nil
}

func (mc *multiComboInfo) buildMessageCombo(param *giftSendParam) (singleCombo, multiCombo *livegifts.Combo) {
	multiCombo = &livegifts.Combo{
		ID:         mc.mc.comboID,
		Num:        int(mc.userRankNum),
		TotalNum:   int(mc.mc.giftNum),
		RemainTime: gift.ComboRemainDuration(mc.mc.giftNum * param.g.Price).Milliseconds(),
		Top1UserID: mc.top1UserID,
	}
	multiCombo.BuildEffect(param.g, mc.mc.previousGiftNum)

	singleCombo = &livegifts.Combo{
		ID:         mc.sc.comboID,
		Num:        int(mc.sc.giftNum),
		RemainTime: gift.ComboRemainDuration(mc.sc.giftNum * param.g.Price).Milliseconds(),
	}
	if multiCombo.EffectURL != "" {
		// WORKAROUND: 兼容 Web, iOS < 6.0.7, 安卓 < 6.0.7 的版本, 一起送特效需要放在单人特效中下发
		singleCombo.EffectURL = multiCombo.EffectURL
		singleCombo.WebEffectURL = multiCombo.WebEffectURL
	}
	return singleCombo, multiCombo
}

func (mc *multiComboInfo) saveCombo(param *giftSendParam) error {
	now := goutil.TimeNow()
	remainDuration := gift.ComboRemainDuration(int64(param.GiftNum) * param.g.Price)
	remainTime := now.Add(remainDuration)
	var err error
	param.lg.OID, err = livegifts.UpdateSave(param.lg, &remainTime, primitive.NilObjectID)
	if err != nil {
		return err
	}
	mc.sc.comboID = param.lg.OID.Hex()
	mc.sc.giftNum = int64(param.GiftNum)
	pipe := service.Redis.TxPipeline()
	pipe.HMSet(mc.sc.key, map[string]interface{}{
		mc.sc.fieldComboID:       mc.sc.comboID,
		mc.sc.fieldComboGiftNum:  mc.sc.giftNum,
		mc.sc.fieldComboLastTime: now.UnixMilli(),
	})
	pipe.Expire(mc.sc.key, 20*time.Second)

	mc.mc.comboID = primitive.NewObjectID().Hex()
	mc.mc.giftNum = int64(param.GiftNum)
	pipe.HMSet(mc.mc.key, map[string]interface{}{
		mc.mc.fieldComboID:       mc.mc.comboID,
		mc.mc.fieldComboGiftNum:  mc.mc.giftNum,
		mc.mc.fieldComboLastTime: now.UnixMilli(),
	})
	pipe.Expire(mc.mc.key, 20*time.Second)
	pipe.HDel(mc.mc.key, mc.mc.fieldComboLock)
	comboUserRankKey := keys.KeyRoomMultiComboUsersRank1.Format(mc.mc.comboID)
	rankNumCmd := pipe.ZIncrBy(comboUserRankKey, float64(param.GiftNum), strconv.FormatInt(mc.userID, 10))
	pipe.Expire(comboUserRankKey, 24*time.Hour)
	_, err = pipe.Exec()
	if err != nil {
		return err
	}
	mc.userRankNum = int64(rankNumCmd.Val())
	mc.top1UserID = mc.userID // 首次送礼新建一起送连击的时候，当前用户即为送礼最多的用户
	param.combo, param.multiCombo = mc.buildMessageCombo(param)
	return nil
}

func (mc *multiComboInfo) updateCombo(param *giftSendParam) error {
	var (
		now          = goutil.TimeNow()
		singleNumCmd *redis.IntCmd
	)
	pipe := service.Redis.TxPipeline()
	// 一起送中可能会包含用户多次单人连击，单人连击不存在时会创建新的
	if mc.sc.comboID == "" {
		remainDuration := gift.ComboRemainDuration(int64(param.GiftNum) * param.g.Price)
		remainTime := now.Add(remainDuration)
		var err error
		param.lg.OID, err = livegifts.UpdateSave(param.lg, &remainTime, primitive.NilObjectID)
		if err != nil {
			return err
		}
		mc.sc.comboID = param.lg.OID.Hex()
		mc.sc.giftNum = int64(param.GiftNum)
		pipe.HMSet(mc.sc.key, map[string]interface{}{
			mc.sc.fieldComboID:       mc.sc.comboID,
			mc.sc.fieldComboGiftNum:  mc.sc.giftNum,
			mc.sc.fieldComboLastTime: now.UnixMilli(),
		})
	} else {
		singleNumCmd = pipe.HIncrBy(mc.sc.key, mc.sc.fieldComboGiftNum, int64(param.GiftNum))
		pipe.HSet(mc.sc.key, mc.sc.fieldComboLastTime, now.UnixMilli())
	}
	pipe.Expire(mc.sc.key, 20*time.Second)
	multiNumCmd := pipe.HIncrBy(mc.mc.key, mc.mc.fieldComboGiftNum, int64(param.GiftNum))
	pipe.HSet(mc.mc.key, mc.mc.fieldComboLastTime, now.UnixMilli())
	pipe.HDel(mc.mc.key, mc.mc.fieldComboLock)
	pipe.Expire(mc.mc.key, 20*time.Second)
	comboUserRankKey := keys.KeyRoomMultiComboUsersRank1.Format(mc.mc.comboID)
	rankNumCmd := pipe.ZIncrBy(comboUserRankKey, float64(param.GiftNum), strconv.FormatInt(mc.userID, 10))
	top1Cmd := pipe.ZRevRange(comboUserRankKey, 0, 0)
	pipe.Expire(comboUserRankKey, 24*time.Hour)
	_, err := pipe.Exec()
	if err != nil {
		return err
	}
	mc.mc.giftNum = multiNumCmd.Val()
	mc.mc.previousGiftNum = mc.mc.giftNum - int64(param.GiftNum)
	mc.userRankNum = int64(rankNumCmd.Val())
	top1Res := top1Cmd.Val()
	if len(top1Res) > 0 {
		mc.top1UserID, err = strconv.ParseInt(top1Res[0], 10, 64)
		if err != nil {
			logger.WithField("combo_id", mc.mc.comboID).Error(err)
			// PASS
		}
	}

	if mc.sc.comboID != "" && singleNumCmd != nil {
		mc.sc.giftNum = singleNumCmd.Val()
		remainDuration := gift.ComboRemainDuration(mc.sc.giftNum * param.g.Price)
		remainTime := now.Add(remainDuration)
		comboOID, err := primitive.ObjectIDFromHex(mc.sc.comboID)
		if err != nil {
			return err
		}
		param.lg.OID, err = livegifts.UpdateSave(param.lg, &remainTime, comboOID)
		if err != nil {
			return err
		}
	}
	param.combo, param.multiCombo = mc.buildMessageCombo(param)
	return nil
}

func (param *giftSendParam) buildUpgradeGift() *livegiftupgrade.UpgradeInfo {
	if param.baseUpgradeGift == nil {
		return nil
	}
	baseUpgrade, upgradeMap, err := livegiftupgrade.FindGiftUpgradeMap(param.baseUpgradeGift.GiftID)
	if err != nil {
		logger.WithField("gift_id", param.baseUpgradeGift.GiftID).Error(err)
		return nil
	}
	if baseUpgrade == nil || len(upgradeMap) == 0 {
		logger.WithField("gift_id", param.baseUpgradeGift.GiftID).Error("gift upgrade is empty")
		return nil
	}

	giftUpgradeRecordMap, err := livegiftupgrade.FindGiftUpgradeRecordsMapByUserID(param.user.UserID(), param.baseUpgradeGift.GiftID,
		param.baseUpgradeGift.ToggleTime)
	if err != nil {
		logger.WithField("gift_id", param.baseUpgradeGift.GiftID).Error(err)
		return nil
	}
	if livegiftupgrade.IsAllCollected(upgradeMap, giftUpgradeRecordMap) {
		// 集齐所有升级礼物后不再需获取 upgrade 信息
		return nil
	}

	upsertGiftUpgradeParam := usermeta.UpsertGiftUpgradeParam{
		UserID:                param.user.UserID(),
		GiftID:                param.baseUpgradeGift.GiftID,
		ToggleTime:            param.baseUpgradeGift.ToggleTime,
		AddSendCount:          param.GiftNum,
		FirstUpgradeThreshold: baseUpgrade.MoreInfo.FirstUpgradeNum,
		UpgradeThreshold:      baseUpgrade.MoreInfo.UpgradeNum,
	}
	// 更新 user_meta 中的礼物升级信息
	afterUpgrade, err := upsertGiftUpgradeParam.Upsert()
	if err != nil {
		logger.WithFields(logger.Fields{
			"gift_id": param.baseUpgradeGift.GiftID,
			"user_id": param.user.UserID(),
		}).Error(err)
		return nil
	}

	upgradeInfo := &livegiftupgrade.UpgradeInfo{
		Discount:   livegiftupgrade.BuildDiscountIcon(afterUpgrade, baseUpgrade),
		UpgradeNum: livegiftupgrade.BuildUpgradeNum(afterUpgrade, baseUpgrade),
	}
	return upgradeInfo
}

func (param *giftSendParam) addRevenueRank() {
	if param.r.Limit != nil {
		return
	}
	// TODO: 关播后的本场榜处理
	score := param.lg.Price
	if score == 0 {
		return
	}
	userID := param.user.UserID()
	err := roomsrank.AddRevenue(param.r.RoomID, userID, score, goutil.IntToBool(param.r.Status.Open))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	rankChange, err := usersrank.AddRevenue(param.r.CreatorID, param.r.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err = room.NotifyHourRank(rankChange, param.r)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	err = liverevenues.AddGiftRevenue(userID, param.r.OID, param.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	err = param.r.ReceiveGift(param.GiftNum, param.lg.GiftPrice)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *giftSendParam) addPK() {
	if param.r.Limit != nil {
		return
	}
	score, freeScore := param.g.PKScores(param.GiftNum)
	elems, err := livepk.AddPKScore(param.r, param.user.UserID(), score, freeScore)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if len(elems) != 0 {
		param.broadcastElems = append(param.broadcastElems, elems...)
	}
}

func (param *giftSendParam) addMedalPoint() {
	if param.r.Limit != nil || param.r.Medal == nil {
		return
	}
	medalPoint := param.g.MedalPoint(param.GiftNum)
	if medalPoint == 0 {
		return
	}
	medalParam := livemedalstats.AddPointParam{
		RoomOID:    param.r.OID,
		RoomID:     param.r.RoomID,
		CreatorID:  param.r.CreatorID,
		FromRoomID: param.FromRoomID,
		UserID:     param.user.UserID(),
		UV:         param.uv,
		MedalName:  param.r.Medal.Name,
		Type:       livemedal.TypeGiftAddMedalPoint,
		Source:     livemedal.ChangeSourceGift,
		PointAdd:   medalPoint,
		Scene:      livemedalpointlog.SceneTypePayGift,
		IsRoomOpen: param.r.IsOpen(),
	}
	if param.SendType == sendTypeObtainMedal {
		medalParam.Source = livemedal.ChangeSourceOneClick
		medalParam.From = livemedal.FromOneClickObtain
	}
	medalUpdatedInfo, err := medalParam.AddPoint()
	if err != nil {
		logger.Error(err)
		return
	}
	notifyParam := liveuser.MedalNotifyParam{
		MedalUpdatedInfo: medalUpdatedInfo,
		User:             param.user,
		BubblePtr:        &param.bubble,
		CreatorUsername:  param.r.CreatorUsername,
	}
	notify := notifyParam.NewUserMedalNotify()
	if notify != nil {
		param.broadcastElems = append(param.broadcastElems, notify)
	}
	param.roomMedal = medalUpdatedInfo.After
}

func (param *giftSendParam) addMultiConnectScore() {
	if !param.r.IsMultiConnect() {
		return
	}
	elems, err := livemulticonnect.ScoreHelper{
		Room:   param.r,
		Gift:   param.g,
		Num:    int64(param.GiftNum),
		UserID: param.user.UserID(),
	}.AddScore()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if len(elems) != 0 {
		param.broadcastElems = append(param.broadcastElems, elems...)
	}
}

func (param *giftSendParam) activatedGiftWall() {
	if param.r.Limit != nil {
		return
	}
	revenue := param.g.Price * int64(param.GiftNum)
	notifyElem, err := giftwall.ActiveGift(param.r, param.user.UserID(), param.g.GiftID, revenue, param.GiftNum)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if notifyElem != nil {
		param.broadcastElems = append(param.broadcastElems, notifyElem)
	}
}

func (param *giftSendParam) addUserContribution() {
	if param.r.Limit != nil {
		return
	}
	pointAdd := param.lg.Price * 10 // 1 钻石 = 10 经验
	if param.uv != nil && param.uv.Info != nil {
		pointAdd = param.uv.Info.ScaleContribution(pointAdd)
	}
	addParam := userstatus.NewAddContributionParams(param.user.UserID(),
		param.RoomID, param.r.CreatorUsername, userstatus.FromGiftSend, param.uv)
	if param.fromRoom != nil {
		addParam.SetFromRoom(param.fromRoom.RoomID, param.fromRoom.CreatorUsername)
	}
	err := addParam.AddPurchaseContribution(pointAdd)
	if err != nil {
		logger.Error(err)
		return
	}
}

func (param *giftSendParam) addLiveSpend() {
	if param.r.Limit != nil {
		return
	}
	roomID := param.RoomID
	if param.FromRoomID > 0 {
		// 主播连线时，用户实际所在的直播间是 FromRoomID
		roomID = param.FromRoomID
	}
	utils.SendLiveSpend(param.user.UserID(), param.lg.Price, roomID)
}

func (param *giftSendParam) buildIMMessage() {
	sendNotify := param.g.AlwaysNotify() || // attr 属性设置飘屏
		(param.g.Price >= gift.ComboNotifyMinPrice && param.g.Effect != "") || // 大礼物飘屏
		(param.combo != nil && param.combo.Notify) || // 单人连击飘屏
		(param.multiCombo != nil && param.multiCombo.Notify) // 一起送连击飘屏

	// 查询用户已佩戴的送礼通知外观
	giftNotification, err := userappearance.FindWornAppearance(param.user.UserID(), appearance.TypeGiftNotification)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// 房间送礼消息
	roomMsg := param.lg.RoomMessage()
	if giftNotification != nil {
		roomMsg.GiftNotification = userappearance.NewGiftNotification(giftNotification)
	}
	param.broadcastElems = append(param.broadcastElems,
		param.lg.BuildBroadcastMessage(!sendNotify && param.r.FilterGiftMessage(), roomMsg),
	)

	if param.FromRoomID != 0 {
		// 跨房间送礼消息
		crossRoomMsg := param.lg.CrossRoomMessage(param.FromRoomID, param.r)
		if giftNotification != nil {
			crossRoomMsg.GiftNotification = userappearance.NewGiftNotification(giftNotification)
		}
		param.broadcastElems = append(param.broadcastElems,
			param.lg.BuildCrossRoomBroadcastMessage(param.FromRoomID, param.r, crossRoomMsg),
		)
	}

	if param.r.Limit != nil {
		// 礼物房没有全站飘屏
		return
	}
	// 全站飘屏
	if !sendNotify {
		// 未设置成总是飘屏和价值不够
		return
	}

	nb := gift.NotifyBuilder{
		RoomID:          param.RoomID,
		CreatorUsername: param.r.CreatorUsername,
		User:            param.user,
		Bubble:          param.bubble,
		Gift:            param.g,
		GiftNum:         param.GiftNum,
	}
	// 一起送 multiCombo 存在时，飘屏需要展示一起送的总连击数量，优先级高于单人连击的总数量
	if param.multiCombo != nil {
		if param.multiCombo.Top1UserID == 0 {
			// 没有正确获取到助攻王信息，不再进行飘屏
			return
		}
		nb.ComboNum = param.multiCombo.TotalNum
		top1, err := liveuser.FindOneSimple(bson.M{"user_id": param.multiCombo.Top1UserID}, nil)
		if err != nil {
			logger.WithField("user_id", param.multiCombo.Top1UserID).Error(err)
			// PASS
			return
		}
		if top1 == nil {
			logger.WithField("user_id", param.multiCombo.Top1UserID).Error("top1 is nil")
			return
		}
		nb.MultiComboTop1Username = top1.Username
	} else if param.combo != nil {
		nb.ComboNum = param.combo.Num
	}
	payload := livegifts.ComboNotifyPayload{
		NotifyPayload: nb.Build(),
		Combo:         param.combo,
		MultiCombo:    param.multiCombo,
	}
	param.broadcastElems = append(param.broadcastElems, &userapi.BroadcastElem{
		Type:    liveim.IMMessageTypeAll,
		RoomID:  param.RoomID,
		Payload: payload,
	})
}

func (param *giftSendParam) broadcast() {
	err := userapi.BroadcastMany(param.broadcastElems)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *giftSendParam) addActivity() {
	if param.r.Limit != nil {
		return
	}
	r := rank.
		NewSyncParam(param.r.RoomID, param.user.UserID(), param.r.CreatorID).
		SetTransaction(param.balance).
		SetOpenLogID(param.r.Status.OpenLogID).
		SetGuildID(param.r.GuildID).
		SetActivityCatalogID(param.r.ActivityCatalogID).
		SetGift(param.g, param.GiftNum)
	r.AddRankPoint()
	r.SendLiveActivity(param.uc)
}

func (param *giftSendParam) addLiveShow() {
	if param.r.Limit != nil {
		return
	}
	liveshow.
		NewSyncLiveShow(param.RoomID, param.user.UserID(), param.r.CreatorID).
		SetGift(param.g.GiftID, param.g.Price, param.GiftNum).
		Sync()
}

func (param *giftSendParam) addRoomPaidUser() {
	if param.g.Type == gift.TypeFree {
		return
	}
	chatroom.AddCurrentRoomPaidUser(param.RoomID, param.user.UserID(), param.r.Status.OpenTime)
}

func (param *giftSendParam) addFansBoxEnergy() {
	contributions := []fansboxenergy.GiftContribution{{Gift: param.g, Num: param.GiftNum}}
	elems := fansboxenergy.AddGiftContributions(param.r, param.user.UserID(), contributions)
	param.broadcastElems = append(param.broadcastElems, elems...)
}
