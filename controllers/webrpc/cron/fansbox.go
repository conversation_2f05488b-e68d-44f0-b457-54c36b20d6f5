package cron

import (
	"fmt"
	"math/rand"
	"time"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansbox"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxtask"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type fansBoxTaskParam struct {
	now             time.Time
	roomIDs         []int64
	levelFansBoxMap map[int]*livefansbox.LiveFansBox
	broadcastElems  []*userapi.BroadcastElem
}

// 粉丝团宝箱任务初始化 websocket 消息
type fansBoxTaskNewMessage struct {
	Type    string  `json:"type"`
	Event   string  `json:"event"`
	RoomID  int64   `json:"room_id"`
	FansBox fansBox `json:"fans_box"`
}

// 粉丝团宝箱信息
type fansBox struct {
	Name    string  `json:"name"`     // 宝箱名称
	IconURL string  `json:"icon_url"` // 宝箱 icon
	BoxTask boxTask `json:"box_task"`
}

// boxTask 粉丝团宝箱任务信息
type boxTask struct {
	ID              int64 `json:"id"`
	TargetEnergy    int   `json:"target_energy"`
	CurrentEnergy   int   `json:"current_energy"`
	Status          int   `json:"status"`
	RefreshDuration int64 `json:"refresh_duration"` // 刷新倒计时（单位：毫秒）
}

// ActionCronFansBoxTaskReset 每日重置粉丝团宝箱任务
// 运行周期：40 59 23 * * * 每天 23 点 59 分 40 秒执行
/**
 * @api {post} /rpc/live-service/cron/fans-box/task/reset 每日重置粉丝团宝箱任务
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "message": "重置粉丝团宝箱任务成功，共重置 100 个直播间的粉丝团宝箱任务，给开播中的 20 个直播间发送了重置粉丝团宝箱任务的消息",
 *     "data": null
 *   }
 */
func ActionCronFansBoxTaskReset(c *handler.Context) (handler.ActionResponse, string, error) {
	param := new(fansBoxTaskParam)
	// 允许最多提前两分钟执行
	param.now = goutil.TimeNow().Add(2 * time.Minute)
	if !livefansbox.EnableFansBox(param.now) {
		return nil, "宝箱功能暂未开启", nil
	}
	// 查询开通粉丝勋章的直播间（预估有 2-3w 开通粉丝勋章的直播间）
	var err error
	param.roomIDs, err = room.FindRoomIDs(bson.M{"medal": bson.M{"$exists": true}})
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if len(param.roomIDs) <= 0 {
		return nil, "当前没有开通粉丝勋章的直播间", nil
	}

	// 重置粉丝团宝箱任务
	err = param.resetFansBox()
	if err != nil {
		return nil, "", err
	}

	// 给开播中的直播间发送重置粉丝团宝箱任务 websocket 消息
	err = param.sendFansBoxBroadcast()
	if err != nil {
		return nil, "", err
	}

	logStr := fmt.Sprintf("重置粉丝团宝箱任务成功，共重置 %d 个直播间的粉丝团宝箱任务，给开播中的 %d 个直播间发送了重置粉丝团宝箱任务的消息",
		len(param.roomIDs), len(param.broadcastElems))
	logger.Info(logStr)
	return nil, logStr, nil
}

func (param *fansBoxTaskParam) resetFansBox() error {
	if len(param.roomIDs) <= 0 {
		return nil
	}

	var err error
	param.levelFansBoxMap, err = livefansbox.FindLevelFansBoxMap()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	roomIDFansCountMap, err := livemedal.RoomIDFansMedalCountMap(param.roomIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	fansBoxTasks := make([]*livefansboxtask.LiveFansBoxTask, 0, len(param.roomIDs))
	bizdate := param.now.Format(goutil.TimeFormatYMD)
	for roomID, fansCount := range roomIDFansCountMap {
		var fansBox *livefansbox.LiveFansBox
		for level := livefansbox.MaxLevel; level > 0; level-- {
			if fansCount >= param.levelFansBoxMap[level].FansCount {
				fansBox = param.levelFansBoxMap[level]
				break
			}
		}
		if fansBox == nil {
			logger.Errorf("未匹配到粉丝数对应的粉丝团宝箱信息，fans_count: %d", fansCount)
			continue
		}

		fansBoxTasks = append(fansBoxTasks, &livefansboxtask.LiveFansBoxTask{
			Bizdate:   bizdate,
			RoomID:    roomID,
			FansCount: fansCount,
			Level:     fansBox.Level,
			Energy:    0,
			Status:    livefansboxtask.StatusUnfinished,
		})
	}

	err = servicedb.Tx(livefansboxtask.LiveFansBoxTask{}.DB(), func(tx *gorm.DB) error {
		// 插入的数量级预估 2-3w，分批插入，每 1000 条插入一次
		err = servicedb.SplitBatchInsert(tx, livefansboxtask.LiveFansBoxTask{}.TableName(), fansBoxTasks, 1000, false)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

// sendFansBoxBroadcast 发送重置粉丝团宝箱任务 websocket 消息
func (param *fansBoxTaskParam) sendFansBoxBroadcast() error {
	// 开播中的房间 IDs
	openRoomIDs, err := room.OpenRoomIDs()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(openRoomIDs) <= 0 {
		return nil
	}
	// 开通粉丝勋章且正在开播中的直播间
	roomIDs := util.IntersectionInt64(openRoomIDs, param.roomIDs)
	tasks, err := livefansboxtask.ListByRoomIDs(roomIDs, param.now)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(tasks) <= 0 {
		return nil
	}

	refreshTime := goutil.BeginningOfDay(param.now).UnixMilli()
	param.broadcastElems = make([]*userapi.BroadcastElem, 0, len(roomIDs))
	for _, task := range tasks {
		fansbox, ok := param.levelFansBoxMap[task.Level]
		if !ok {
			logger.Errorf("未匹配到对应等级的粉丝团宝箱信息，level: %d", task.Level)
			continue
		}
		// 刷新倒计时，错峰请求，随机增加 0 ~ 5s，即零点后 0 ~ 5s 内刷新
		refreshDuration := refreshTime - goutil.TimeNow().UnixMilli() + rand.Int63n(5e3)
		// 只给开播中的房间发送粉丝团宝箱任务初始化 websocket 消息
		payload := fansBoxTaskNewMessage{
			Type:   liveim.TypeFansBox,
			Event:  liveim.EventFansBoxTaskNew,
			RoomID: task.RoomID,
			FansBox: fansBox{
				Name:    "粉团宝箱",
				IconURL: storage.ParseSchemeURL(config.Conf.Params.MedalParams.FansBoxDefaultIcon),
				BoxTask: boxTask{
					ID:              task.ID,
					TargetEnergy:    fansbox.Energy,
					CurrentEnergy:   task.Energy,
					Status:          task.Status,
					RefreshDuration: refreshDuration,
				},
			},
		}
		param.broadcastElems = append(param.broadcastElems, &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  task.RoomID,
			Payload: payload,
		})
	}

	// 给开播中的直播间发送重置粉丝团宝箱任务 websocket 消息
	if len(param.broadcastElems) > 0 {
		err := userapi.BroadcastMany(param.broadcastElems)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	return nil
}
