package cron

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansbox"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxtask"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(fansBoxTaskNewMessage{}, "type", "event", "room_id", "fans_box")
	kc.Check(fansBox{}, "name", "icon_url", "box_task")
	kc.Check(boxTask{}, "id", "target_energy", "current_energy", "status", "refresh_duration")
}

func TestActionCronFansBoxTaskReset(t *testing.T) {
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cancel()

	// 测试重置成功
	uri := "/rpc/cron/fans-box/task/reset"
	c := handler.NewTestContext(http.MethodPost, uri, false, handler.M{})
	_, _, err := ActionCronFansBoxTaskReset(c)
	require.NoError(err)
}

func TestFansBoxTaskParam_resetFansBox(t *testing.T) {
	require := require.New(t)

	testRoomID1 := int64(100000000)
	testRoomID2 := int64(100000001)
	testRoomID3 := int64(100000002)
	roomIDs := []int64{testRoomID1, testRoomID2, testRoomID3}
	testMedals := []livemedal.Simple{
		{RoomID: testRoomID1, CreatorID: 11, UserID: 99997, Status: livemedal.StatusOwned},
		{RoomID: testRoomID2, CreatorID: 12, UserID: 99998, Status: livemedal.StatusOwned},
		{RoomID: testRoomID3, CreatorID: 13, UserID: 99999, Status: livemedal.StatusPending},
	}

	// 插入测试数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	medals := make([]interface{}, 0, len(testMedals))
	for _, m := range testMedals {
		medals = append(medals, m)
	}
	res, err := livemedal.Collection().InsertMany(ctx, medals)
	require.NoError(err)
	require.Equal(len(testMedals), len(res.InsertedIDs))
	defer func() {
		_, _ = livemedal.Collection().DeleteMany(ctx, bson.M{"room_id": handler.M{"$in": roomIDs}})
	}()

	param := &fansBoxTaskParam{
		now:     goutil.TimeNow(),
		roomIDs: roomIDs,
	}
	// 删除测试数据
	require.NoError(livefansboxtask.LiveFansBoxTask{}.DB().Delete("", "room_id IN (?) AND bizdate = ?",
		roomIDs, param.now.Format(goutil.TimeFormatYMD)).Error)

	// 测试重置粉丝团宝箱
	err = param.resetFansBox()
	require.NoError(err)

	// 验证数据插入成功
	var tasks []*livefansboxtask.LiveFansBoxTask
	require.NoError(livefansboxtask.LiveFansBoxTask{}.DB().Where("room_id IN (?) AND bizdate = ?",
		roomIDs, param.now.Format(goutil.TimeFormatYMD)).Find(&tasks).Error)
	require.Len(tasks, 3)
}

func TestFansBoxTaskParam_sendFansBoxBroadcast(t *testing.T) {
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cancel()

	testRoomID1 := int64(100000000)
	testRoomID2 := int64(100000001)
	testRoomID3 := int64(100000002)
	roomIDs := []int64{testRoomID1, testRoomID2, testRoomID3}

	// 生成房间数据
	roomList := make([]interface{}, 0, len(roomIDs))
	for i, roomID := range roomIDs {
		roomList = append(roomList, room.Room{
			Helper: room.Helper{
				RoomID:    roomID,
				Name:      "测试开播直播间",
				NameClean: fmt.Sprintf("测试开播直播间 %d", 20000+i),
				Status:    room.Status{Open: room.StatusOpenTrue},
			},
		})
	}
	ctx, cancel1 := service.MongoDB.Context()
	defer cancel1()
	// 删除测试数据
	_, err := room.Collection().DeleteMany(ctx, bson.M{"room_id": bson.M{"$in": roomIDs}})
	require.NoError(err)
	_, err = room.Collection().InsertMany(ctx, roomList)
	require.NoError(err)

	levelFansBoxMap, err := livefansbox.FindLevelFansBoxMap()
	require.NoError(err)
	param := &fansBoxTaskParam{
		now:             goutil.TimeNow(),
		roomIDs:         roomIDs,
		levelFansBoxMap: levelFansBoxMap,
	}
	err = param.sendFansBoxBroadcast()
	require.NoError(err)
}
