# GitHub Copilot Instructions for Live-Service Project

## Project Overview

This is a Go-based live streaming service project with the following key characteristics:

- Backend service for live streaming platform
- Uses MongoDB for data storage
- Implements Go best practices and coding standards
- Has comprehensive unit testing coverage

## Core Coding Standards

### 1. Go Code Structure Optimization

**Always prefer flat, linear code structure over nested conditionals:**

```go
// ✅ Good: Use guard clauses for early returns
func ProcessData(data *Data) error {
    if data == nil {
        return ErrNilData
    }

    if !data.IsValid() {
        return ErrInvalidData
    }

    if !data.HasPermission() {
        return ErrNoPermission
    }

    // Main logic here
    return processLogic(data)
}

// ❌ Bad: Nested if statements
func ProcessData(data *Data) error {
    if data != nil {
        if data.IsValid() {
            if data.HasPermission() {
                return processLogic(data)
            } else {
                return ErrNoPermission
            }
        } else {
            return ErrInvalidData
        }
    } else {
        return ErrNilData
    }
}
```

### 2. Go Import Organization

Organize imports in exactly three groups with blank lines between:

```go
import (
    // Standard library (first group)
    "fmt"
    "context"
    "time"

    // Third-party packages (second group)
    "github.com/jinzhu/gorm"
    "go.mongodb.org/mongo-driver/bson"

    // Internal packages (third group)
    "github.com/MiaoSiLa/live-service/models/vip"
    "github.com/MiaoSiLa/live-service/util"
    "github.com/MiaoSiLa/missevan-go/logger"
    goutil "github.com/MiaoSiLa/missevan-go/util"
)
```

**Critical alias rules:**

- `github.com/MiaoSiLa/missevan-go/util` MUST use `goutil` alias
- `github.com/MiaoSiLa/live-service/util` MUST NOT use any alias

### 3. Database Transaction Standards

**Always use servicedb.Tx wrapper for database transactions:**

```go
// ✅ Correct: Use servicedb.Tx wrapper
func UpdateData(id int64, data *Data) error {
    return servicedb.Tx(service.DB, func(tx *gorm.DB) error {
        if err := updateTable1(tx, id, data); err != nil {
            return err
        }

        if err := updateTable2(tx, id, data); err != nil {
            return err
        }

        return nil
    })
}

// ❌ Wrong: Manual transaction management
func UpdateData(id int64, data *Data) error {
    tx := service.DB.Begin()
    defer tx.Rollback()

    // ... operations ...

    return tx.Commit().Error
}
```

**Transaction function naming:**

- Functions used within transactions should have `Tx` suffix
- Example: `UpdateEnergyByIDTx(tx *gorm.DB, ...)`

### 4. MongoDB Context Usage

**Always use service.MongoDB.Context() instead of context.Background():**

```go
// ✅ Correct
ctx, cancel := service.MongoDB.Context()
defer cancel()
result, err := Collection().Operation(ctx, ...)

// ❌ Wrong
result, err := Collection().Operation(context.Background(), ...)
```

### 5. Unique Constraint Handling

**Use servicedb.IsUniqueError() for database unique constraint conflicts:**

```go
// ✅ Correct
err := db.Create(newData).Error
if err != nil {
    if servicedb.IsUniqueError(err) {
        return db.Where("key = ?", key).Updates(updateData).Error
    }
    return err
}
return nil

// ❌ Wrong: Manual error string checking
if strings.Contains(err.Error(), "UNIQUE constraint failed") {
    // ...
}
```

### 6. Go Comment Style

**Never use numbered lists in comments:**

```go
// ✅ Good: Simple, clear comments
// 查询或创建今日宝箱任务
// 如果宝箱任务已完成，则不需要更新
// 更新宝箱任务能量值

// ❌ Bad: Numbered comments
// 1. 查询或创建今日宝箱任务
// 2. 如果宝箱任务已完成，则不需要更新
// 3. 更新宝箱任务能量值
```

### 7. Testing Best Practices

**Use package-level assert/require functions:**

```go
// ✅ Good: Package-level functions
assert.Equal(t, expected, actual)
require.NoError(t, err)
require.NotNil(t, result)

// ❌ Bad: New() pattern
assert := assert.New(t)
require := require.New(t)
```

**Critical rule for nil checks:**

- Use `require.NotNil(t, obj)` when accessing fields afterward
- Use `assert.NotNil(t, obj)` only when it's the last assertion

**MongoDB testing patterns:**

- Use subtests with `t.Run()`
- Insert test data directly into MongoDB instead of mocking
- Always clean up test data with defer statements
- Use "test" prefix for test variables

## Error Handling Patterns

1. **Early returns** for error conditions
2. **Consistent error wrapping** with context
3. **Proper logging** at appropriate levels
4. **Graceful degradation** where applicable

## Code Generation Guidelines

When generating code for this project:

1. **Follow the structure optimization rules** - avoid deep nesting
2. **Use proper import organization** with the three-group pattern
3. **Apply transaction patterns** for database operations
4. **Include appropriate error handling** with early returns
5. **Write comprehensive tests** following the testing patterns
6. **Use clear, descriptive variable names** with appropriate prefixes
7. **Document complex logic** with clear comments (no numbered lists)

## Concept Explanation

When suggesting code changes that relate to established software engineering concepts, always explain the relevant concepts at the end of the response. This helps with:

- Learning and reinforcing development knowledge
- Using correct terminology for communication
- Understanding the principles behind the solutions

Examples of concepts to explain:

- Guard Clauses (when flattening nested conditionals)
- Dependency Inversion Principle (when introducing interfaces)
- Single Responsibility Principle (when refactoring functions)
- Database Transaction Patterns (when using servicedb.Tx)

Remember: The goal is to write maintainable, readable, and robust Go code that follows established best practices and project conventions.
