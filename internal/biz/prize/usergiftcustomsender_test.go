package prize

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNewUserGiftCustomSender(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 准备测试数据
	testUserID := int64(876554454)
	testGiftID := int64(97456642)
	testPrize := &liveprize.Prize{
		ID:        200,
		Type:      liveprize.TypeUserGiftCustom,
		ElementID: testGiftID,
		Duration:  3600,
	}
	distributor := NewUserDistributor(testUserID, testPrize, WithBiz(liveprize.BizFansBoxTask, 233))
	sender := newUserGiftCustomSender(distributor)
	require.NotNil(sender)
	assert.Equal(testPrize.ID, sender.ID)
	assert.Equal(testGiftID, sender.ElementID)
	assert.Equal(testUserID, sender.userID)
}

func TestUserGiftCustomSender_send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(189545648)
	testGiftID := int64(9454842)
	testPrizeID := int64(395465)
	// 清理测试记录
	require.NoError(liveprize.DB().Delete(&liveprize.PrizeLog{}).Where("user_id = ?", testUserID).Error)
	require.NoError(livecustom.LiveCustom{}.DB().Delete("", "custom_type = ? AND element_id = ?",
		livecustom.TypeUserCustomGift, testUserID).Error)
	testPrize := &liveprize.Prize{
		ID:        testPrizeID,
		Type:      liveprize.TypeUserGiftCustom,
		ElementID: testGiftID,
		Duration:  int64(3600),
	}
	distributor := NewUserDistributor(testUserID, testPrize, WithBiz(liveprize.BizFansBoxTask, 233))
	sender := newUserGiftCustomSender(distributor)
	log, err := sender.send()
	require.NoError(err)
	require.NotNil(log)
	// 验证奖品日志记录
	var pl liveprize.PrizeLog
	err = liveprize.DB().Where("id = ? AND status = ?", log.ID, liveprize.StatusReceived).Take(&pl).Error
	require.NoError(err)
	require.NotNil(pl)
	assert.Equal(testUserID, pl.UserID)
	assert.Equal(testPrize.ID, pl.PrizeID)
	// 验证用户定制礼物是否创建成功
	custom, err := livecustom.FindUserCustomGift(testUserID, testGiftID)
	require.NoError(err)
	require.NotNil(custom)
	// 验证开始时间和结束时间
	now := goutil.TimeNow().Unix()
	duration := int64(3600)
	expectedStartTime := now
	expectedEndTime := expectedStartTime + duration
	assert.Equal(expectedStartTime, custom.StartTime)
	assert.Equal(expectedEndTime, custom.EndTime)
	assert.Equal(livecustom.SourceDefault, custom.Source)

	// 测试继续发放（使用过期结束时间点）
	testPrize.ExpireTime = now + (3600 * 2)
	distributor = NewUserDistributor(testUserID, testPrize, WithBiz(liveprize.BizFansBoxTask, 233))
	sender2 := newUserGiftCustomSender(distributor)
	log, err = sender2.send()
	require.NoError(err)
	require.NotNil(log)
	// 验证奖品日志记录
	var pl2 liveprize.PrizeLog
	err = liveprize.DB().Where("id = ? AND status = ?", log.ID, liveprize.StatusReceived).Take(&pl2).Error
	require.NoError(err)
	require.NotNil(pl)
	assert.Equal(testUserID, pl.UserID)
	assert.Equal(testPrize.ID, pl.PrizeID)
	// 验证用户定制礼物是否创建成功
	custom2, err := livecustom.FindUserCustomGift(testUserID, testGiftID)
	require.NoError(err)
	require.NotNil(custom2)
	// 验证开始时间和结束时间
	assert.Equal(expectedStartTime, custom2.StartTime)
	assert.Equal(testPrize.ExpireTime, custom2.EndTime)
	assert.Equal(livecustom.SourceDefault, custom2.Source)
}
