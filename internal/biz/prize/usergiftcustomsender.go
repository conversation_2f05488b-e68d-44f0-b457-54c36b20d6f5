package prize

import (
	"errors"
	"time"

	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type userGiftCustomSender struct {
	*liveprize.Prize
	userID int64
	opt    *option
}

func newUserGiftCustomSender(distributor Distributor) *userGiftCustomSender {
	return &userGiftCustomSender{
		Prize:  distributor.Prize(),
		userID: distributor.ReceiverID(),
		opt:    distributor.Option(),
	}
}

func (sender *userGiftCustomSender) send() (*liveprize.PrizeLog, error) {
	log := &liveprize.PrizeLog{
		Biz:     sender.opt.biz.bizType,
		BizID:   sender.opt.biz.bizID,
		PrizeID: sender.ID,
		UserID:  sender.userID,
	}

	return log.Logging(func() (err error) {
		duration := sender.Duration
		if duration <= 0 {
			logger.WithField("prize_id", sender.ID).Errorf("用户专属礼物资格奖品 duration 配置错误")
			return errors.New("用户专属礼物资格奖品配置错误")
		}
		// 获取该奖励的开始和结束时间
		_, sendEndTime, err := sender.GetTime()
		if err != nil {
			return err
		}
		userID := sender.userID
		giftID := sender.ElementID
		customGift, err := livecustom.FindUserCustomGift(userID, giftID)
		if err != nil {
			return err
		}
		var startTime int64
		if customGift != nil {
			startTime = customGift.EndTime
		} else {
			startTime = goutil.TimeNow().Unix()
		}
		if sendEndTime != 0 {
			remainDuration := sendEndTime - startTime
			if remainDuration <= 0 {
				// 已经奖励到最大时间限制
				return nil
			}
			// 若奖励的结束时间小于过期时间，则以奖励的结束时间为准
			duration = min(duration, remainDuration)
		}

		return livecustom.AddUserCustomGift(userID, giftID, time.Duration(duration)*time.Second, false)
	})
}
