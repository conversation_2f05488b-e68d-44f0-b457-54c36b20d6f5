package fansboxenergy

import (
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxtask"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
)

// GiftContribution 礼物贡献信息
type GiftContribution struct {
	Gift *gift.Gift
	Num  int
}

// AddGiftContributions 为粉丝团宝箱贡献礼物能量
func AddGiftContributions(r *room.Room, userID int64, contributions []GiftContribution) []*userapi.BroadcastElem {
	// 只有开播状态才能贡献能量
	if !r.IsOpen() {
		return nil
	}

	// 查询当日粉丝团宝箱任务
	task, err := livefansboxtask.FindTodayTask(r.RoomID)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id": userID,
			"room_id": r.RoomID,
		}).Error("查询粉丝团宝箱当日任务失败：", err)
		return nil
	}
	if task == nil {
		return nil
	}

	var broadcastElems []*userapi.BroadcastElem

	// 为每个礼物贡献能量
	for _, contrib := range contributions {
		// 贡献礼物能量
		updated, refreshed, err := task.ContributeFromGift(livefansboxtask.GiftContribution{
			RoomID:  r.RoomID,
			UserID:  userID,
			Gift:    contrib.Gift,
			GiftNum: contrib.Num,
		})
		if err != nil {
			logger.WithFields(logger.Fields{
				"user_id": userID,
				"room_id": r.RoomID,
				"gift_id": contrib.Gift.GiftID,
			}).Error("粉丝团宝箱能量贡献处理失败：", err)
			continue
		}

		if !updated {
			continue
		}

		if !refreshed {
			logger.WithFields(logger.Fields{
				"user_id": userID,
				"room_id": r.RoomID,
				"gift_id": contrib.Gift.GiftID,
			}).Warn("粉丝团宝箱能量更新成功但数据刷新失败")
			continue
		}

		// 返回任务更新消息
		taskUpdateMessage := task.NewTaskUpdateMessage()
		broadcastElems = append(broadcastElems, &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  task.RoomID,
			Payload: taskUpdateMessage,
		})
	}

	return broadcastElems
}
