package fansboxenergy

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansbox"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxtask"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"go.mongodb.org/mongo-driver/bson"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	m.Run()
}

func TestAddGiftContributions(t *testing.T) {
	testUserID := int64(12345)
	testRoomID := int64(67890)
	testCreatorID := int64(54321)
	testRoom := &room.Room{
		Helper: room.Helper{
			RoomID:    testRoomID,
			CreatorID: testCreatorID,
		},
	}
	testGiftNum := 5
	testGift := &gift.Gift{
		GiftID: 1001,
		Price:  100,
		Type:   gift.TypeNormal,
	}

	clearTask := func(t *testing.T) {
		require.NoError(t, livefansboxtask.LiveFansBoxTask{}.DB().Delete("", "room_id = ?", testRoomID).Error)
	}

	t.Run("房间未开播时返回 nil", func(t *testing.T) {
		testRoom.Status.Open = room.StatusOpenFalse

		contributions := []GiftContribution{{Gift: testGift, Num: testGiftNum}}
		result := AddGiftContributions(testRoom, testUserID, contributions)
		assert.Nil(t, result)
	})

	t.Run("查询不到当日粉丝团宝箱任务时返回 nil", func(t *testing.T) {
		clearTask(t)
		testRoom.Status.Open = room.StatusOpenTrue

		contributions := []GiftContribution{{Gift: testGift, Num: testGiftNum}}
		result := AddGiftContributions(testRoom, testUserID, contributions)
		assert.Nil(t, result)
	})

	t.Run("成功贡献能量并返回广播消息", func(t *testing.T) {
		clearTask(t)
		testRoom.Status.Open = room.StatusOpenTrue

		// 创建测试任务
		now := goutil.TimeNow()
		todayFormat := now.Format(goutil.TimeFormatYMD)
		testTask := &livefansboxtask.LiveFansBoxTask{
			Level:     livefansbox.Level1,
			Bizdate:   todayFormat,
			RoomID:    testRoomID,
			FansCount: 10,
			Energy:    100,
			Status:    livefansboxtask.StatusUnfinished,
		}
		require.NoError(t, livefansboxtask.LiveFansBoxTask{}.DB().Create(testTask).Error)

		// 创建测试勋章
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		testMedal := &livemedal.LiveMedal{
			Simple: livemedal.Simple{
				RoomID: testRoomID,
				UserID: testUserID,
				Status: livemedal.StatusOwned,
			},
		}
		insertResult, err := livemedal.Collection().InsertOne(ctx, testMedal)
		require.NoError(t, err)
		defer func() {
			_, err := livemedal.Collection().DeleteOne(ctx, bson.M{"_id": insertResult.InsertedID})
			require.NoError(t, err)
		}()

		contributions := []GiftContribution{{Gift: testGift, Num: testGiftNum}}
		result := AddGiftContributions(testRoom, testUserID, contributions)
		require.Len(t, result, 1)
		assert.Equal(t, liveim.IMMessageTypeNormal, result[0].Type)
		assert.Equal(t, testRoomID, result[0].RoomID)
		assert.NotNil(t, result[0].Payload)

		// 验证任务更新消息的类型
		taskUpdateMessage, ok := result[0].Payload.(*livefansboxtask.TaskUpdateMessage)
		require.NotNil(t, taskUpdateMessage)
		require.True(t, ok)
		assert.Equal(t, liveim.TypeFansBox, taskUpdateMessage.Type)
		assert.Equal(t, liveim.EventFansBoxTaskUpdate, taskUpdateMessage.Event)
		assert.Equal(t, testRoomID, taskUpdateMessage.RoomID)
		assert.NotNil(t, taskUpdateMessage.FansBox)
		assert.NotNil(t, taskUpdateMessage.FansBox.BoxTask)
	})

	t.Run("礼物类型不符合条件时返回 nil", func(t *testing.T) {
		clearTask(t)
		testRoom.Status.Open = room.StatusOpenTrue
		testGift.Type = gift.TypeFree
		defer func() {
			testGift.Type = gift.TypeNormal
		}()
		contributions := []GiftContribution{{Gift: testGift, Num: testGiftNum}}
		result := AddGiftContributions(testRoom, testUserID, contributions)
		assert.Nil(t, result)
	})

	t.Run("用户非粉丝团成员时返回 nil", func(t *testing.T) {
		clearTask(t)
		testRoom.Status.Open = room.StatusOpenTrue

		// 创建测试任务
		now := goutil.TimeNow()
		todayFormat := now.Format(goutil.TimeFormatYMD)
		testTask := &livefansboxtask.LiveFansBoxTask{
			Level:     livefansbox.Level1,
			Bizdate:   todayFormat,
			RoomID:    testRoomID,
			FansCount: 10,
			Energy:    100,
			Status:    livefansboxtask.StatusUnfinished,
		}
		require.NoError(t, livefansboxtask.LiveFansBoxTask{}.DB().Create(testTask).Error)

		// 确保用户没有勋章
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, err := livemedal.Collection().DeleteMany(ctx, bson.M{
			"user_id": testUserID,
			"room_id": testRoomID,
		})
		require.NoError(t, err)

		contributions := []GiftContribution{{Gift: testGift, Num: testGiftNum}}
		result := AddGiftContributions(testRoom, testUserID, contributions)
		assert.Nil(t, result)
	})
}
