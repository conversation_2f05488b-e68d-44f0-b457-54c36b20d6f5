package livefansachievement

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/jinzhu/gorm"
)

// fansAchievementPopupMessage 粉丝成就弹窗延时消息
type fansAchievementPopupMessage struct {
	RoomID int64 `json:"room_id"`
}

// DelayFansAchievementPopupOperator 粉丝成就弹窗延时消息 - 消费者
func DelayFansAchievementPopupOperator() func(*databus.Message) {
	return func(message *databus.Message) {
		if !keys.DelayKeyFansAchievementPopup1.MatchKey(message.Key) {
			return
		}

		var msg fansAchievementPopupMessage
		err := json.Unmarshal(message.Value, &msg)
		if err != nil {
			logger.Error(err)
			return
		}

		err = processFansAchievementPopup(msg.RoomID)
		if err != nil {
			logger.WithField("room_id", msg.RoomID).Error(err)
			return
		}
	}
}

// processFansAchievementPopup 处理粉丝成就弹窗逻辑
func processFansAchievementPopup(roomID int64) error {
	r, err := room.Find(roomID)
	if err != nil {
		return err
	}
	if r == nil {
		logger.WithField("room_id", roomID).Warn("room not found")
		return nil
	}

	if r.Status.Open != room.StatusOpenTrue {
		return nil
	}

	now := goutil.TimeNow().UnixMilli()
	if now-r.Status.OpenTime < (time.Minute * 10).Milliseconds() {
		return nil
	}

	var highestRecord *FansRoomAchievement
	err = servicedb.Tx(DB(), func(tx *gorm.DB) error {
		achievement, err := FindHighestByRoomID(tx, roomID)
		if err != nil {
			return err
		}
		if achievement == nil {
			return fmt.Errorf("未找到成就记录，room_id: %d", roomID)
		}

		if achievement.PopupStatus != PopupStatusNotSend {
			return nil
		}

		ok, err := achievement.MarkAsSent(tx)
		if err != nil {
			return err
		}
		if !ok {
			return fmt.Errorf(
				"乐观锁更新失败，可能成就状态已改变，room_id: %d, achievement_config_id: %d, popup_status: %d",
				roomID, achievement.AchievementConfigID, achievement.PopupStatus,
			)
		}

		_, err = achievement.InvalidateLowerUnsent(tx)
		if err != nil {
			return err
		}

		highestRecord = achievement
		return nil
	})
	if err != nil {
		return err
	}

	if highestRecord == nil {
		return fmt.Errorf("未找到成就记录，room_id: %d", roomID)
	}

	err = sendAchievementPopupMessage(highestRecord)
	if err != nil {
		return fmt.Errorf(
			"发送成就弹窗消息失败，room_id: %d，achievement_config_id: %d，err: %v",
			highestRecord.RoomID, highestRecord.AchievementConfigID, err,
		)
	}

	return nil
}

// formatAchievementTips 格式化成就提示信息
func formatAchievementTips(createTime int64) string {
	return time.Unix(createTime, 0).Format("2006/01/02") + " 达成"
}

// sendAchievementPopupMessage 发送成就弹窗消息
func sendAchievementPopupMessage(record *FansRoomAchievement) error {
	config, err := FindAchievementConfigByID(DB(), record.AchievementConfigID)
	if err != nil {
		return err
	}
	if config == nil || config.MoreInfo == nil || config.MoreInfo.Reward == nil || config.MoreInfo.Reward.Popup == nil || config.MoreInfo.Reward.Popup.ImageURL == "" {
		return fmt.Errorf("成就配置异常，config_id: %d", record.AchievementConfigID)
	}

	message := NewAchievementPopupMessage(
		record.RoomID,
		config.MoreInfo.Reward.Popup.ImageURL,
		formatAchievementTips(record.CreateTime),
	)
	return userapi.Broadcast(record.RoomID, message)
}
