package livefansachievement

import (
	"encoding/json"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// fansAchievementConfigTableName 粉丝成就信息配置表的表名
const fansAchievementConfigTableName = "live_fans_achievement_config"

// FansAchievementConfig 粉丝成就信息配置表
type FansAchievementConfig struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	FansCount    int64  `gorm:"column:fans_count"`
	Name         string `gorm:"column:name"`
	More         string `gorm:"column:more"`

	MoreInfo *More `gorm:"-"`
}

// More 更多信息
type More struct {
	Reward *Reward `json:"reward,omitempty"`
}

// Reward 奖励信息
type Reward struct {
	Popup     *Popup     `json:"popup,omitempty"`
	RedPacket *RedPacket `json:"red_packet,omitempty"`
	Medal     *Medal     `json:"medal,omitempty"`
}

// Popup 特效信息
type Popup struct {
	Image string `json:"image,omitempty"`

	ImageURL string `json:"image_url,omitempty"`
}

// RedPacket 红包信息
type RedPacket struct {
	PrizeID int `json:"prize_id,omitempty"`
}

// Medal 勋章信息
type Medal struct {
	Icon            string `json:"icon,omitempty"`
	BackgroundImage string `json:"background_image,omitempty"`
	TextColor       string `json:"text_color,omitempty"`

	IconURL            string `json:"icon_url,omitempty"`
	BackgroundImageURL string `json:"background_image_url,omitempty"`
}

// AfterFind gorm hook
func (fac *FansAchievementConfig) AfterFind() (err error) {
	if fac.More != "" {
		fac.MoreInfo, err = fac.UnmarshalMore()
		if err != nil {
			return err
		}
	}
	return nil
}

// UnmarshalMore 解析 More 字段
func (fac *FansAchievementConfig) UnmarshalMore() (*More, error) {
	if fac.More == "" {
		return nil, nil
	}
	var more *More
	err := json.Unmarshal([]byte(fac.More), &more)
	if err != nil {
		return nil, err
	}
	if more != nil && more.Reward != nil {
		if more.Reward.Medal != nil {
			if more.Reward.Medal.BackgroundImage != "" {
				more.Reward.Medal.BackgroundImageURL = storage.ParseSchemeURL(more.Reward.Medal.BackgroundImage)
			}
			if more.Reward.Medal.Icon != "" {
				more.Reward.Medal.IconURL = storage.ParseSchemeURL(more.Reward.Medal.Icon)
			}
		}
		if more.Reward.Popup != nil && more.Reward.Popup.Image != "" {
			more.Reward.Popup.ImageURL = storage.ParseSchemeURL(more.Reward.Popup.Image)
		}
	}
	return more, nil
}

// TableName 表名
func (*FansAchievementConfig) TableName() string {
	return fansAchievementConfigTableName
}

// findAllAchievementConfigs 查找所有粉丝成就配置信息
func findAllAchievementConfigs(db *gorm.DB) ([]*FansAchievementConfig, error) {
	var achievementConfigs []*FansAchievementConfig
	err := db.Table(fansAchievementConfigTableName).Order("fans_count ASC").Find(&achievementConfigs).Error
	if err != nil {
		return nil, err
	}
	return achievementConfigs, nil
}

// FindAllAchievementConfigsWithCache 使用缓存，查找所有粉丝成就配置信息
func FindAllAchievementConfigsWithCache(db *gorm.DB) ([]*FansAchievementConfig, error) {
	key := keys.KeyFansAchievementConfigList0.Format()
	achievementsBytes, err := service.LRURedis.Get(key).Bytes()
	var list []*FansAchievementConfig
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		// PASS
	} else if len(achievementsBytes) != 0 {
		err = json.Unmarshal(achievementsBytes, &list)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			return list, nil
		}
	}
	achievementConfigs, err := findAllAchievementConfigs(db)
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	cacheByte, err := json.Marshal(&achievementConfigs)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err = service.LRURedis.Set(key, string(cacheByte), 5*time.Minute).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return achievementConfigs, nil
}

// FindAchievementConfigByID 根据 ID 查找单个粉丝成就配置信息
func FindAchievementConfigByID(db *gorm.DB, configID int64) (*FansAchievementConfig, error) {
	configs, err := FindAllAchievementConfigsWithCache(db)
	if err != nil {
		return nil, err
	}

	for _, config := range configs {
		if config.ID == configID {
			return config, nil
		}
	}

	return nil, nil
}
