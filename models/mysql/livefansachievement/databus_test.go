package livefansachievement

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func createTestRoom(t *testing.T, roomID int64, open int, openTime int64) {
	collection := service.MongoDB.Collection(room.CollectionName)
	roomName := fmt.Sprintf("测试房间_%d", roomID)
	_, err := collection.DeleteMany(context.Background(), bson.M{
		"room_id": roomID,
	})
	require.NoError(t, err)

	testRoom := room.Helper{
		RoomID:          roomID,
		Name:            roomName,
		NameClean:       roomName,
		Type:            "live",
		Status:          room.Status{Open: open, OpenTime: openTime},
		CreatorID:       roomID, // 使用房间 ID 作为主播 ID
		CreatorUsername: "test_user",
		CreatedTime:     goutil.TimeNow(),
		UpdatedTime:     goutil.TimeNow(),
	}

	_, err = collection.InsertOne(context.Background(), testRoom)
	require.NoError(t, err)

	t.Cleanup(func() {
		_, err := collection.DeleteMany(context.Background(), bson.M{"room_id": roomID})
		require.NoError(t, err)
	})
}

func TestFormatAchievementTips(t *testing.T) {
	tests := []struct {
		name       string
		createTime int64
		expected   string
	}{
		{
			name:       "正常时间戳",
			createTime: 1609459200, // 2021-01-01 00:00:00 UTC
			expected:   "2021/01/01 达成",
		},
		{
			name:       "零时间戳",
			createTime: 0, // 1970-01-01 00:00:00 UTC
			expected:   "1970/01/01 达成",
		},
		{
			name:       "闰年日期",
			createTime: 1582934400, // 2020-02-29 00:00:00 UTC
			expected:   "2020/02/29 达成",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := formatAchievementTips(tt.createTime)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestProcessFansAchievementPopup(t *testing.T) {
	t.Run("房间不存在时返回 nil", func(t *testing.T) {
		nonExistentRoomID := int64(999999)

		err := processFansAchievementPopup(nonExistentRoomID)
		require.NoError(t, err) // 房间不存在时应该返回 nil
	})

	t.Run("房间未开播时返回 nil", func(t *testing.T) {
		roomID := int64(700001)

		// 创建一个未开播的房间
		createTestRoom(t, roomID, room.StatusOpenFalse, goutil.TimeNow().Unix())

		err := processFansAchievementPopup(roomID)
		require.NoError(t, err) // 房间未开播时应该返回 nil
	})

	t.Run("开播时间不足 10 分钟时返回 nil", func(t *testing.T) {
		roomID := int64(700002)

		// 创建一个开播时间不足 10 分钟的房间
		openTime := goutil.TimeNow().Add(-5 * time.Minute).UnixMilli() // 5 分钟前开播（毫秒）
		createTestRoom(t, roomID, room.StatusOpenTrue, openTime)

		err := processFansAchievementPopup(roomID)
		require.NoError(t, err) // 开播时间不足 10 分钟时应该返回 nil
	})

	t.Run("没有成就记录时返回错误", func(t *testing.T) {
		roomID := int64(700003)

		// 创建一个开播超过 10 分钟的房间，但没有成就记录
		openTime := goutil.TimeNow().Add(-15 * time.Minute).UnixMilli() // 15 分钟前开播（毫秒）
		createTestRoom(t, roomID, room.StatusOpenTrue, openTime)

		err := processFansAchievementPopup(roomID)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "未找到成就记录")
	})

	t.Run("成就已发放时返回错误", func(t *testing.T) {
		db := DB()
		roomID := int64(700004)

		// 创建一个开播超过 10 分钟的房间
		openTime := goutil.TimeNow().Add(-15 * time.Minute).UnixMilli()
		createTestRoom(t, roomID, room.StatusOpenTrue, openTime)

		// 创建一个已发放的成就记录
		testRecord := &FansRoomAchievement{
			RoomID:              roomID,
			AchievementConfigID: 1,
			FansCount:           50,
			PopupStatus:         PopupStatusAlreadySend, // 已发放
			CreateTime:          1609459200,
		}
		ok, err := testRecord.Create(db)
		require.NoError(t, err)
		require.True(t, ok)

		t.Cleanup(func() {
			err := db.Where("room_id = ?", roomID).Delete(&FansRoomAchievement{}).Error
			require.NoError(t, err)
		})

		err = processFansAchievementPopup(roomID)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "未找到成就记录")
	})

	t.Run("sendAchievementPopupMessage 失败时返回错误", func(t *testing.T) {
		db := DB()
		roomID := int64(700006)

		// 创建一个开播超过 10 分钟的房间
		openTime := goutil.TimeNow().Add(-15 * time.Minute).UnixMilli()
		createTestRoom(t, roomID, room.StatusOpenTrue, openTime)

		// 创建一个未发放的成就记录
		testRecord := &FansRoomAchievement{
			RoomID:              roomID,
			AchievementConfigID: 1,
			FansCount:           70,
			PopupStatus:         PopupStatusNotSend,
			CreateTime:          1609459200,
		}
		ok, err := testRecord.Create(db)
		require.NoError(t, err)
		require.True(t, ok)

		t.Cleanup(func() {
			err := db.Where("room_id = ?", roomID).Delete(&FansRoomAchievement{}).Error
			require.NoError(t, err)
		})

		// Mock sendAchievementPopupMessage 返回错误
		cleanup := mrpc.SetMock("im://broadcast", func(input interface{}) (interface{}, error) {
			return nil, errors.New("broadcast failed")
		})
		defer cleanup()

		err = processFansAchievementPopup(roomID)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "发送成就弹窗消息失败")
		assert.Contains(t, err.Error(), "broadcast failed")
	})

	t.Run("成功处理成就弹窗", func(t *testing.T) {
		db := DB()
		roomID := int64(700007)

		// 创建一个开播超过 10 分钟的房间
		openTime := goutil.TimeNow().Add(-15 * time.Minute).UnixMilli()
		createTestRoom(t, roomID, room.StatusOpenTrue, openTime)

		// 创建多个成就记录，包括一个最高等级的未发放记录
		// 注意：只使用存在的配置 ID (1)，通过不同的 FansCount 来区分等级
		records := []*FansRoomAchievement{
			{
				RoomID:              roomID,
				AchievementConfigID: 1,
				FansCount:           30,
				PopupStatus:         PopupStatusNotSend,
				CreateTime:          1609459200,
			},
			{
				RoomID:              roomID,
				AchievementConfigID: 1,
				FansCount:           80, // 最高等级
				PopupStatus:         PopupStatusNotSend,
				CreateTime:          1609459200,
			},
			{
				RoomID:              roomID,
				AchievementConfigID: 1,
				FansCount:           50,
				PopupStatus:         PopupStatusNotSend,
				CreateTime:          1609459200,
			},
		}

		for _, record := range records {
			ok, err := record.Create(db)
			require.NoError(t, err)
			require.True(t, ok)
		}

		// 注册清理函数，测试结束后删除创建的成就记录
		t.Cleanup(func() {
			db.Where("room_id = ?", roomID).Delete(&FansRoomAchievement{})
		})

		// Mock sendAchievementPopupMessage 成功
		var broadcastCalled bool
		var broadcastRoomID int64
		cleanup := mrpc.SetMock("im://broadcast", func(input interface{}) (interface{}, error) {
			broadcastCalled = true
			var inputData map[string]interface{}
			err := json.Unmarshal(input.(json.RawMessage), &inputData)
			if err == nil {
				if roomIDFloat, ok := inputData["room_id"].(float64); ok {
					broadcastRoomID = int64(roomIDFloat)
				}
			}
			return true, nil
		})
		defer cleanup()

		err := processFansAchievementPopup(roomID)
		require.NoError(t, err)

		// 验证 broadcast 被调用
		assert.True(t, broadcastCalled)
		assert.Equal(t, roomID, broadcastRoomID)

		// 验证最高等级的记录被标记为已发放
		highestRecord, err := FindHighestByRoomID(db, roomID)
		require.NoError(t, err)
		require.NotNil(t, highestRecord)
		assert.Equal(t, 80, highestRecord.FansCount) // 应该是最高等级的记录
		assert.Equal(t, PopupStatusAlreadySend, highestRecord.PopupStatus)

		// 验证低等级的记录被标记为无需发放
		lowerRecords, err := GetByRoomIDAndPopupStatus(db, roomID, PopupStatusNoNeedSend)
		require.NoError(t, err)
		assert.Len(t, lowerRecords, 2) // 应该有两个低等级记录被标记为无需发放

		for _, record := range lowerRecords {
			assert.Less(t, record.FansCount, 80) // 确保是低等级记录
		}
	})
}

func TestSendAchievementPopupMessage(t *testing.T) {
	t.Run("成功发送成就弹窗消息", func(t *testing.T) {
		// Mock userapi.Broadcast
		var broadcastCalled bool
		var broadcastRoomID int64
		var broadcastMessage interface{}

		cleanup := mrpc.SetMock("im://broadcast", func(input interface{}) (interface{}, error) {
			broadcastCalled = true
			// 解析输入参数 - input 是 json.RawMessage
			var inputData map[string]interface{}
			err := json.Unmarshal(input.(json.RawMessage), &inputData)
			if err == nil {
				if roomID, exists := inputData["room_id"]; exists {
					if roomIDFloat, ok := roomID.(float64); ok {
						broadcastRoomID = int64(roomIDFloat)
					}
				}
				if payload, exists := inputData["payload"]; exists {
					broadcastMessage = payload
				}
			}
			return true, nil
		})
		defer cleanup()

		// 创建测试记录
		record := &FansRoomAchievement{
			RoomID:              12345,
			AchievementConfigID: 1,          // 使用测试数据中存在的配置 ID
			CreateTime:          1609459200, // 2021-01-01 00:00:00 UTC
		}

		// 调用函数
		err := sendAchievementPopupMessage(record)
		require.NoError(t, err)

		// 验证 Broadcast 被调用
		assert.True(t, broadcastCalled, "userapi.Broadcast 应该被调用")
		assert.Equal(t, int64(12345), broadcastRoomID, "房间 ID 应该正确")

		// 验证消息结构
		messageMap, ok := broadcastMessage.(map[string]interface{})
		require.True(t, ok)
		assert.Equal(t, "medal", messageMap["type"])
		assert.Equal(t, "achievement_popup", messageMap["event"])
		assert.Equal(t, float64(12345), messageMap["room_id"])

		achievement, ok := messageMap["achievement"]
		require.True(t, ok)

		achievementMap, ok := achievement.(map[string]interface{})
		require.True(t, ok)
		assert.Contains(t, achievementMap["image_url"].(string), "medal/image.png")
		assert.Equal(t, "2021/01/01 达成", achievementMap["tips"])
	})

	t.Run("成就配置不存在时返回错误", func(t *testing.T) {
		// 创建使用不存在配置 ID 的测试记录
		record := &FansRoomAchievement{
			RoomID:              12345,
			AchievementConfigID: 99999, // 不存在的配置 ID
			CreateTime:          1609459200,
		}

		// 调用函数
		err := sendAchievementPopupMessage(record)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "成就配置异常")
	})

	t.Run("Broadcast 失败时返回错误", func(t *testing.T) {
		// Mock userapi.Broadcast 返回错误
		cleanup := mrpc.SetMock("im://broadcast", func(input interface{}) (interface{}, error) {
			return nil, errors.New("broadcast failed")
		})
		defer cleanup()

		// 创建测试记录
		record := &FansRoomAchievement{
			RoomID:              12345,
			AchievementConfigID: 1,
			CreateTime:          1609459200,
		}

		// 调用函数
		err := sendAchievementPopupMessage(record)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "broadcast failed")
	})
}
