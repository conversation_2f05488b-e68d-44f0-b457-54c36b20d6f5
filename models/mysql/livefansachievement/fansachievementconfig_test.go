package livefansachievement

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(&FansAchievementConfig{}, "id", "create_time", "modified_time", "fans_count", "name", "more")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(&More{}, "reward")
	kc.Check(&Reward{}, "popup", "red_packet", "medal")
}

func TestLiveFansAchievement_AfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	item := &FansAchievementConfig{
		More: `{"reward":{"popup":{"image":"oss://medal/image.png"},"red_packet":{"prize_id":234},"medal":{"icon":"oss://medal/icon.png","background_image":"oss://medal/background.png","text_color":"#FFF666"}}}`,
	}
	err := item.AfterFind()
	require.NoError(err)
	require.NotNil(item.MoreInfo)
	require.NotNil(item.MoreInfo.Reward)
	require.NotNil(item.MoreInfo.Reward.Popup)
	assert.EqualValues("oss://medal/image.png", item.MoreInfo.Reward.Popup.Image)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/image.png"), item.MoreInfo.Reward.Popup.ImageURL)
	require.NotNil(item.MoreInfo.Reward.RedPacket)
	assert.EqualValues(234, item.MoreInfo.Reward.RedPacket.PrizeID)

	require.NotNil(item.MoreInfo.Reward.Medal)
	assert.EqualValues("oss://medal/icon.png", item.MoreInfo.Reward.Medal.Icon)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/icon.png"), item.MoreInfo.Reward.Medal.IconURL)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/background.png"), item.MoreInfo.Reward.Medal.BackgroundImageURL)
	assert.EqualValues("#FFF666", item.MoreInfo.Reward.Medal.TextColor)
}

func TestLiveFansAchievement_UnmarshalMore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	item := &FansAchievementConfig{
		More: `{"reward":{"popup":{"image":"oss://medal/image.png"},"red_packet":{"prize_id":234},"medal":{"icon":"oss://medal/icon.png","background_image":"oss://medal/background.png","text_color":"#FFF666"}}}`,
	}
	more, err := item.UnmarshalMore()
	require.NoError(err)
	require.NotNil(more)
	require.NotNil(more.Reward)
	require.NotNil(more.Reward.Popup)
	assert.EqualValues("oss://medal/image.png", more.Reward.Popup.Image)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/image.png"), more.Reward.Popup.ImageURL)
	require.NotNil(more.Reward.RedPacket)
	assert.EqualValues(234, more.Reward.RedPacket.PrizeID)

	require.NotNil(more.Reward.Medal)
	assert.EqualValues("oss://medal/icon.png", more.Reward.Medal.Icon)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/icon.png"), more.Reward.Medal.IconURL)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/background.png"), more.Reward.Medal.BackgroundImageURL)
	assert.EqualValues("#FFF666", more.Reward.Medal.TextColor)
	require.NotNil(more.Reward.Medal)
}

func TestFindAllAchievements(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	items, err := findAllAchievementConfigs(DB())
	require.NoError(err)
	require.NotNil(items)
	assert.EqualValues(1, items[0].ID)
	assert.EqualValues(10, items[0].FansCount)
	assert.EqualValues("10 档位", items[0].Name)
	require.NotNil(items[0].MoreInfo)
	require.NotNil(items[0].MoreInfo.Reward)
	require.NotNil(items[0].MoreInfo.Reward.Popup)
	assert.EqualValues("oss://medal/image.png", items[0].MoreInfo.Reward.Popup.Image)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/image.png"), items[0].MoreInfo.Reward.Popup.ImageURL)
	require.NotNil(items[0].MoreInfo.Reward.RedPacket)
	assert.EqualValues(234, items[0].MoreInfo.Reward.RedPacket.PrizeID)

	require.NotNil(items[0].MoreInfo.Reward.Medal)
	assert.EqualValues("oss://medal/icon.png", items[0].MoreInfo.Reward.Medal.Icon)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/icon.png"), items[0].MoreInfo.Reward.Medal.IconURL)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/background.png"), items[0].MoreInfo.Reward.Medal.BackgroundImageURL)
	assert.EqualValues("#FFF666", items[0].MoreInfo.Reward.Medal.TextColor)
}

func TestFindAllAchievementsWithCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	t.Run("测试没有缓存，从数据库读取数据", func(t *testing.T) {
		key := keys.KeyFansAchievementConfigList0.Format()
		require.NoError(service.LRURedis.Del(key).Err())

		items, err := FindAllAchievementConfigsWithCache(DB())
		require.NoError(err)
		require.NotNil(items)
		assert.EqualValues(1, items[0].ID)
		assert.EqualValues(10, items[0].FansCount)
		assert.EqualValues("10 档位", items[0].Name)
		require.NotNil(items[0].MoreInfo)
		require.NotNil(items[0].MoreInfo.Reward)
		require.NotNil(items[0].MoreInfo.Reward.Popup)
		assert.EqualValues("oss://medal/image.png", items[0].MoreInfo.Reward.Popup.Image)
		assert.EqualValues(storage.ParseSchemeURL("oss://medal/image.png"), items[0].MoreInfo.Reward.Popup.ImageURL)
		require.NotNil(items[0].MoreInfo.Reward.RedPacket)
		assert.EqualValues(234, items[0].MoreInfo.Reward.RedPacket.PrizeID)

		require.NotNil(items[0].MoreInfo.Reward.Medal)
		assert.EqualValues("oss://medal/icon.png", items[0].MoreInfo.Reward.Medal.Icon)
		assert.EqualValues(storage.ParseSchemeURL("oss://medal/icon.png"), items[0].MoreInfo.Reward.Medal.IconURL)
		assert.EqualValues(storage.ParseSchemeURL("oss://medal/background.png"), items[0].MoreInfo.Reward.Medal.BackgroundImageURL)
		assert.EqualValues("#FFF666", items[0].MoreInfo.Reward.Medal.TextColor)

		achievementsBytes, err := service.LRURedis.Get(key).Bytes()
		require.NoError(err)
		require.Greater(len(achievementsBytes), 0)
		var list []*FansAchievementConfig
		err = json.Unmarshal(achievementsBytes, &list)
		require.NoError(err)
		require.NotNil(list)

		assert.EqualValues(1, list[0].ID)
		assert.EqualValues(10, list[0].FansCount)
		assert.EqualValues("10 档位", list[0].Name)
		require.NotNil(list[0].MoreInfo)
		require.NotNil(list[0].MoreInfo.Reward)
		require.NotNil(list[0].MoreInfo.Reward.Popup)
		assert.EqualValues("oss://medal/image.png", list[0].MoreInfo.Reward.Popup.Image)
		assert.EqualValues(storage.ParseSchemeURL("oss://medal/image.png"), items[0].MoreInfo.Reward.Popup.ImageURL)
		require.NotNil(items[0].MoreInfo.Reward.RedPacket)
		assert.EqualValues(234, list[0].MoreInfo.Reward.RedPacket.PrizeID)

		require.NotNil(list[0].MoreInfo.Reward.Medal)
		assert.EqualValues("oss://medal/icon.png", list[0].MoreInfo.Reward.Medal.Icon)
		assert.EqualValues(storage.ParseSchemeURL("oss://medal/icon.png"), items[0].MoreInfo.Reward.Medal.IconURL)
		assert.EqualValues(storage.ParseSchemeURL("oss://medal/background.png"), items[0].MoreInfo.Reward.Medal.BackgroundImageURL)
		assert.EqualValues("#FFF666", list[0].MoreInfo.Reward.Medal.TextColor)
	})
}
