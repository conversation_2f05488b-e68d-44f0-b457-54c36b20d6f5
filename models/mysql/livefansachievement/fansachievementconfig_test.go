package livefansachievement

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(&FansAchievementConfig{}, "id", "create_time", "modified_time", "fans_count", "name", "more")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(&More{}, "reward")
	kc.Check(&Reward{}, "popup", "red_packet", "medal")
}

func TestLiveFansAchievement_AfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	item := &FansAchievementConfig{
		More: `{"reward":{"popup":{"image":"oss://medal/image.png"},"red_packet":{"prize_id":234},"medal":{"icon":"oss://medal/icon.png","background_image":"oss://medal/background.png","text_color":"#FFF666"}}}`,
	}
	err := item.AfterFind()
	require.NoError(err)
	require.NotNil(item.MoreInfo)
	require.NotNil(item.MoreInfo.Reward)
	require.NotNil(item.MoreInfo.Reward.Popup)
	assert.EqualValues("oss://medal/image.png", item.MoreInfo.Reward.Popup.Image)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/image.png"), item.MoreInfo.Reward.Popup.ImageURL)
	require.NotNil(item.MoreInfo.Reward.RedPacket)
	assert.EqualValues(234, item.MoreInfo.Reward.RedPacket.PrizeID)

	require.NotNil(item.MoreInfo.Reward.Medal)
	assert.EqualValues("oss://medal/icon.png", item.MoreInfo.Reward.Medal.Icon)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/icon.png"), item.MoreInfo.Reward.Medal.IconURL)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/background.png"), item.MoreInfo.Reward.Medal.BackgroundImageURL)
	assert.EqualValues("#FFF666", item.MoreInfo.Reward.Medal.TextColor)
}

func TestLiveFansAchievement_UnmarshalMore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	item := &FansAchievementConfig{
		More: `{"reward":{"popup":{"image":"oss://medal/image.png"},"red_packet":{"prize_id":234},"medal":{"icon":"oss://medal/icon.png","background_image":"oss://medal/background.png","text_color":"#FFF666"}}}`,
	}
	more, err := item.UnmarshalMore()
	require.NoError(err)
	require.NotNil(more)
	require.NotNil(more.Reward)
	require.NotNil(more.Reward.Popup)
	assert.EqualValues("oss://medal/image.png", more.Reward.Popup.Image)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/image.png"), more.Reward.Popup.ImageURL)
	require.NotNil(more.Reward.RedPacket)
	assert.EqualValues(234, more.Reward.RedPacket.PrizeID)

	require.NotNil(more.Reward.Medal)
	assert.EqualValues("oss://medal/icon.png", more.Reward.Medal.Icon)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/icon.png"), more.Reward.Medal.IconURL)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/background.png"), more.Reward.Medal.BackgroundImageURL)
	assert.EqualValues("#FFF666", more.Reward.Medal.TextColor)
	require.NotNil(more.Reward.Medal)
}

func TestFindAllAchievements(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	items, err := findAllAchievementConfigs(DB())
	require.NoError(err)
	require.NotNil(items)
	assert.EqualValues(1, items[0].ID)
	assert.EqualValues(10, items[0].FansCount)
	assert.EqualValues("10 档位", items[0].Name)
	require.NotNil(items[0].MoreInfo)
	require.NotNil(items[0].MoreInfo.Reward)
	require.NotNil(items[0].MoreInfo.Reward.Popup)
	assert.EqualValues("oss://medal/image.png", items[0].MoreInfo.Reward.Popup.Image)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/image.png"), items[0].MoreInfo.Reward.Popup.ImageURL)
	require.NotNil(items[0].MoreInfo.Reward.RedPacket)
	assert.EqualValues(234, items[0].MoreInfo.Reward.RedPacket.PrizeID)

	require.NotNil(items[0].MoreInfo.Reward.Medal)
	assert.EqualValues("oss://medal/icon.png", items[0].MoreInfo.Reward.Medal.Icon)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/icon.png"), items[0].MoreInfo.Reward.Medal.IconURL)
	assert.EqualValues(storage.ParseSchemeURL("oss://medal/background.png"), items[0].MoreInfo.Reward.Medal.BackgroundImageURL)
	assert.EqualValues("#FFF666", items[0].MoreInfo.Reward.Medal.TextColor)
}

func TestFindAllAchievementsWithCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	t.Run("测试没有缓存，从数据库读取数据", func(t *testing.T) {
		key := keys.KeyFansAchievementConfigList0.Format()
		require.NoError(service.LRURedis.Del(key).Err())

		items, err := FindAllAchievementConfigsWithCache(DB())
		require.NoError(err)
		require.NotNil(items)
		assert.EqualValues(1, items[0].ID)
		assert.EqualValues(10, items[0].FansCount)
		assert.EqualValues("10 档位", items[0].Name)
		require.NotNil(items[0].MoreInfo)
		require.NotNil(items[0].MoreInfo.Reward)
		require.NotNil(items[0].MoreInfo.Reward.Popup)
		assert.EqualValues("oss://medal/image.png", items[0].MoreInfo.Reward.Popup.Image)
		assert.EqualValues(storage.ParseSchemeURL("oss://medal/image.png"), items[0].MoreInfo.Reward.Popup.ImageURL)
		require.NotNil(items[0].MoreInfo.Reward.RedPacket)
		assert.EqualValues(234, items[0].MoreInfo.Reward.RedPacket.PrizeID)

		require.NotNil(items[0].MoreInfo.Reward.Medal)
		assert.EqualValues("oss://medal/icon.png", items[0].MoreInfo.Reward.Medal.Icon)
		assert.EqualValues(storage.ParseSchemeURL("oss://medal/icon.png"), items[0].MoreInfo.Reward.Medal.IconURL)
		assert.EqualValues(storage.ParseSchemeURL("oss://medal/background.png"), items[0].MoreInfo.Reward.Medal.BackgroundImageURL)
		assert.EqualValues("#FFF666", items[0].MoreInfo.Reward.Medal.TextColor)

		achievementsBytes, err := service.LRURedis.Get(key).Bytes()
		require.NoError(err)
		require.Greater(len(achievementsBytes), 0)
		var list []*FansAchievementConfig
		err = json.Unmarshal(achievementsBytes, &list)
		require.NoError(err)
		require.NotNil(list)

		assert.EqualValues(1, list[0].ID)
		assert.EqualValues(10, list[0].FansCount)
		assert.EqualValues("10 档位", list[0].Name)
		require.NotNil(list[0].MoreInfo)
		require.NotNil(list[0].MoreInfo.Reward)
		require.NotNil(list[0].MoreInfo.Reward.Popup)
		assert.EqualValues("oss://medal/image.png", list[0].MoreInfo.Reward.Popup.Image)
		assert.EqualValues(storage.ParseSchemeURL("oss://medal/image.png"), items[0].MoreInfo.Reward.Popup.ImageURL)
		require.NotNil(items[0].MoreInfo.Reward.RedPacket)
		assert.EqualValues(234, list[0].MoreInfo.Reward.RedPacket.PrizeID)

		require.NotNil(list[0].MoreInfo.Reward.Medal)
		assert.EqualValues("oss://medal/icon.png", list[0].MoreInfo.Reward.Medal.Icon)
		assert.EqualValues(storage.ParseSchemeURL("oss://medal/icon.png"), items[0].MoreInfo.Reward.Medal.IconURL)
		assert.EqualValues(storage.ParseSchemeURL("oss://medal/background.png"), items[0].MoreInfo.Reward.Medal.BackgroundImageURL)
		assert.EqualValues("#FFF666", list[0].MoreInfo.Reward.Medal.TextColor)
	})
}

func TestFindAchievementConfigByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	db := DB()

	// 创建测试数据
	createTestAchievementConfigs := func() []*FansAchievementConfig {
		// 先清理可能存在的测试数据
		db.Table(fansAchievementConfigTableName).Where("id IN (?)", []int64{9001, 9002}).Delete(&FansAchievementConfig{})

		testConfigs := []*FansAchievementConfig{
			{
				ID:           9001,
				CreateTime:   1609459200,
				ModifiedTime: 1609459200,
				FansCount:    50,
				Name:         "50 粉丝成就",
				More:         `{"reward":{"popup":{"image":"oss://test/popup.png"},"medal":{"icon":"oss://test/icon.png","background_image":"oss://test/bg.png","text_color":"#FF0000"}}}`,
			},
			{
				ID:           9002,
				CreateTime:   1609459200,
				ModifiedTime: 1609459200,
				FansCount:    100,
				Name:         "100 粉丝成就",
				More:         `{"reward":{"red_packet":{"prize_id":456}}}`,
			},
		}

		for _, config := range testConfigs {
			err := db.Table(fansAchievementConfigTableName).Create(config).Error
			require.NoError(err)
		}

		// 清理缓存以确保从数据库读取
		key := keys.KeyFansAchievementConfigList0.Format()
		service.LRURedis.Del(key)

		return testConfigs
	}

	// 清理测试数据
	cleanupTestData := func() {
		err := db.Table(fansAchievementConfigTableName).Where("id IN (?)", []int64{9001, 9002}).Delete(&FansAchievementConfig{}).Error
		require.NoError(err)
		// 清理缓存
		key := keys.KeyFansAchievementConfigList0.Format()
		service.LRURedis.Del(key)
	}

	t.Run("正常情况：使用有效的 ID 查找成就配置", func(t *testing.T) {
		testConfigs := createTestAchievementConfigs()
		defer cleanupTestData()

		// 测试查找第一个配置
		config, err := FindAchievementConfigByID(db, 9001)
		require.NoError(err)
		require.NotNil(config)
		assert.EqualValues(9001, config.ID)
		assert.EqualValues(50, config.FansCount)
		assert.EqualValues("50 粉丝成就", config.Name)
		require.NotNil(config.MoreInfo)
		require.NotNil(config.MoreInfo.Reward)
		require.NotNil(config.MoreInfo.Reward.Popup)
		assert.EqualValues("oss://test/popup.png", config.MoreInfo.Reward.Popup.Image)
		assert.EqualValues(storage.ParseSchemeURL("oss://test/popup.png"), config.MoreInfo.Reward.Popup.ImageURL)

		// 测试查找第二个配置
		config, err = FindAchievementConfigByID(db, 9002)
		require.NoError(err)
		require.NotNil(config)
		assert.EqualValues(9002, config.ID)
		assert.EqualValues(100, config.FansCount)
		assert.EqualValues("100 粉丝成就", config.Name)
		require.NotNil(config.MoreInfo)
		require.NotNil(config.MoreInfo.Reward)
		require.NotNil(config.MoreInfo.Reward.RedPacket)
		assert.EqualValues(456, config.MoreInfo.Reward.RedPacket.PrizeID)

		// 测试查找已存在的配置（ID 为 1，来自测试数据）
		config, err = FindAchievementConfigByID(db, 1)
		require.NoError(err)
		require.NotNil(config)
		assert.EqualValues(1, config.ID)
		assert.EqualValues(10, config.FansCount)
		assert.EqualValues("10 档位", config.Name)

		_ = testConfigs // 避免未使用变量警告
	})

	t.Run("边界情况：使用不存在的 ID", func(t *testing.T) {
		createTestAchievementConfigs()
		defer cleanupTestData()

		// 测试不存在的 ID
		config, err := FindAchievementConfigByID(db, 99999)
		require.NoError(err)
		assert.Nil(config, "不存在的 ID 应该返回 nil")
	})
}
