package livefansachievement

// AchievementPopupMessage 粉丝团达成人数成就弹窗消息
type AchievementPopupMessage struct {
	Type        string             `json:"type"`  // 固定为 "medal"
	Event       string             `json:"event"` // 固定为 "achievement_popup"
	RoomID      int64              `json:"room_id"`
	Achievement *AchievementDetail `json:"achievement"`
}

type AchievementDetail struct {
	ImageURL string `json:"image_url"` // 成就展示图
	Tips     string `json:"tips"`      // 成就提示文案
}

// NewAchievementPopupMessage 创建成就弹窗消息
func NewAchievementPopupMessage(roomID int64, imageURL, tips string) *AchievementPopupMessage {
	return &AchievementPopupMessage{
		Type:   "medal",
		Event:  "achievement_popup",
		RoomID: roomID,
		Achievement: &AchievementDetail{
			ImageURL: imageURL,
			Tips:     tips,
		},
	}
}
