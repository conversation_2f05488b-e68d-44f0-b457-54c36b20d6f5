package livefansachievement

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewAchievementPopupMessage(t *testing.T) {
	got := NewAchievementPopupMessage(12345, "https://test.com/img.png", "2025/06/03 达成")
	want := &AchievementPopupMessage{
		Type:   "medal",
		Event:  "achievement_popup",
		RoomID: 12345,
		Achievement: &AchievementDetail{
			ImageURL: "https://test.com/img.png",
			Tips:     "2025/06/03 达成",
		},
	}
	assert.Equal(t, want, got)
}
