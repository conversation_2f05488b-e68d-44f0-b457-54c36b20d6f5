package livefansachievement

import (
	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// PopupStatusNotExist 无弹窗
	PopupStatusNotExist = -2
	// PopupStatusNoNeedSend 无需发放
	PopupStatusNoNeedSend = -1
	// PopupStatusNotSend 未发放
	PopupStatusNotSend = 0
	// PopupStatusAlreadySend 已发放
	PopupStatusAlreadySend = 1
)

// fansRoomAchievementTableName 粉丝人数成就记录表的表名
const fansRoomAchievementTableName = "live_fans_room_achievement"

// FansRoomAchievement 对应数据库中的 live_fans_room_achievement 表
type FansRoomAchievement struct {
	ID                  int64 `gorm:"column:id;primaryKey;autoIncrement"`
	CreateTime          int64 `gorm:"column:create_time"`
	ModifiedTime        int64 `gorm:"column:modified_time"`
	RoomID              int64 `gorm:"column:room_id"`
	AchievementConfigID int64 `gorm:"column:achievement_config_id"`
	FansCount           int   `gorm:"column:fans_count"`
	PopupStatus         int   `gorm:"column:popup_status"`
}

// TableName 返回表名
func (*FansRoomAchievement) TableName() string {
	return fansRoomAchievementTableName
}

// BeforeSave gorm 钩子
func (fra *FansRoomAchievement) BeforeSave() error {
	nowUnix := goutil.TimeNow().Unix()
	fra.ModifiedTime = nowUnix
	if DB().NewRecord(fra) {
		fra.CreateTime = nowUnix
	}
	return nil
}

// Create 新增粉丝成就记录
func (fra *FansRoomAchievement) Create(db *gorm.DB) (bool, error) {
	err := db.Create(fra).Error
	if err != nil {
		if servicedb.IsUniqueError(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// BatchCreate 批量新增粉丝成就记录
func (fra *FansRoomAchievement) BatchCreate(db *gorm.DB, records []*FansRoomAchievement) error {
	return servicedb.SplitBatchInsert(db, fra.TableName(), records, 1000, true)
}

// HasAchievement 用户是否已经达成指定档位的成就
func HasAchievement(db *gorm.DB, roomID, achievementConfigID int64) (bool, error) {
	return servicedb.Exists(db.Table(fansRoomAchievementTableName).
		Where("room_id = ? and achievement_config_id = ?", roomID, achievementConfigID))
}

// GetByRoomIDAndPopupStatus 根据直播间 ID 和弹窗发放状态查询粉丝成就记录
func GetByRoomIDAndPopupStatus(db *gorm.DB, roomID int64, popupStatus int) ([]*FansRoomAchievement, error) {
	var records []*FansRoomAchievement
	err := db.Table(fansRoomAchievementTableName).Where("room_id = ? AND popup_status = ?", roomID, popupStatus).Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, err
}

// UpdatePopupStatus 更新指定成就记录的弹窗发放状态
func (fra *FansRoomAchievement) UpdatePopupStatus(db *gorm.DB, beforePopupStatus, afterPopupStatus int) (int64, error) {
	query := db.Table(fansRoomAchievementTableName).Where("id = ? AND popup_status = ?", fra.ID, beforePopupStatus)
	result := query.Updates(map[string]any{
		"popup_status": afterPopupStatus,
	})
	return result.RowsAffected, result.Error
}

// MarkAsSent 将指定成就记录的弹窗状态从未发放更新为已发放
func (fra *FansRoomAchievement) MarkAsSent(db *gorm.DB) (bool, error) {
	rowsAffected, err := fra.UpdatePopupStatus(db, PopupStatusNotSend, PopupStatusAlreadySend)
	if err != nil {
		return false, err
	}
	ok := rowsAffected > 0
	if ok {
		fra.PopupStatus = PopupStatusAlreadySend
	}
	return ok, nil
}

// FindHighestByRoomID 根据直播间 ID 查询最高等级的成就记录
func FindHighestByRoomID(db *gorm.DB, roomID int64) (*FansRoomAchievement, error) {
	var record FansRoomAchievement
	err := db.Table(fansRoomAchievementTableName).
		Where("room_id = ?", roomID).
		Order("fans_count DESC").
		Take(&record).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

// InvalidateLowerUnsent 作废低等级未发放的成就
// 将当前房间中比当前成就粉丝数量更低且状态为「未发放」的成就标记为「无需发放」
func (fra *FansRoomAchievement) InvalidateLowerUnsent(db *gorm.DB) (int64, error) {
	result := db.Table(fansRoomAchievementTableName).
		Where("room_id = ? AND popup_status = ? AND fans_count < ?",
			fra.RoomID, PopupStatusNotSend, fra.FansCount).
		Updates(map[string]interface{}{
			"popup_status": PopupStatusNoNeedSend,
		})
	return result.RowsAffected, result.Error
}
