package livefansachievement

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCreate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	db := DB()
	// 测试记录创建成功
	l := &FansRoomAchievement{
		RoomID:              645321,
		AchievementConfigID: 1,
		FansCount:           10,
		PopupStatus:         PopupStatusAlreadySend,
	}
	ok, err := l.Create(DB())
	require.NoError(err)
	require.True(ok)
	assert.NotZero(l.ID)
	assert.NotZero(l.CreateTime)
	assert.NotZero(l.ModifiedTime)

	// 验证数据正常
	fansRoomAchievementList, err := GetByRoomIDAndPopupStatus(db, l.RoomID, l.PopupStatus)
	require.NoError(err)
	require.Len(fansRoomAchievementList, 1)
	assert.EqualValues(l.ID, fansRoomAchievementList[0].ID)
	assert.EqualValues(l.<PERSON>, fansRoomAchievementList[0].RoomID)
	assert.EqualValues(l.AchievementConfigID, fansRoomAchievementList[0].AchievementConfigID)
	assert.EqualValues(l.FansCount, fansRoomAchievementList[0].FansCount)
	assert.EqualValues(l.PopupStatus, fansRoomAchievementList[0].PopupStatus)
}

func TestHasAchievement(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(645321)
	achievementConfigID := int64(1)
	db := DB()

	t.Run("用户达到指定成就", func(t *testing.T) {
		ok, err := HasAchievement(db, roomID, achievementConfigID)
		require.NoError(err)
		assert.True(ok)
	})

	t.Run("用户未达到指定成就", func(t *testing.T) {
		ok, err := HasAchievement(db, roomID, 6666)
		require.NoError(err)
		assert.False(ok)
	})
}

func TestGetByRoomIDAndEffectStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(645321)
	db := DB()

	t.Run("获取未发放的成就记录", func(t *testing.T) {
		records, err := GetByRoomIDAndPopupStatus(db, roomID, PopupStatusNotSend)
		require.NoError(err)
		assert.NotEmpty(records)
	})

	t.Run("获取不存在的成就记录", func(t *testing.T) {
		records, err := GetByRoomIDAndPopupStatus(db, roomID, PopupStatusNoNeedSend)
		require.NoError(err)
		require.Len(records, 0)
	})
}

func TestUpdatePopupStatus(t *testing.T) {
	roomID := int64(645321)
	achievementConfigID := int64(1)
	db := DB()

	t.Run("更新未发放的成就记录", func(t *testing.T) {
		// 首先查找一个具体的记录
		records, err := GetByRoomIDAndPopupStatus(db, roomID, PopupStatusNotSend)
		require.NoError(t, err)
		require.NotEmpty(t, records, "需要至少一条未发放的成就记录")

		// 找到指定成就配置 ID 的记录
		var targetRecord *FansRoomAchievement
		for _, record := range records {
			if record.AchievementConfigID == achievementConfigID {
				targetRecord = record
				break
			}
		}
		require.NotNil(t, targetRecord, "需要找到指定成就配置 ID 的记录")

		// 调用结构体方法更新状态
		rowAffected, err := targetRecord.UpdatePopupStatus(db, PopupStatusNotSend, PopupStatusAlreadySend)
		require.NoError(t, err)
		assert.EqualValues(t, 1, rowAffected)
	})

	t.Run("更新已发放的成就记录", func(t *testing.T) {
		// 首先查找一个已发放的记录
		records, err := GetByRoomIDAndPopupStatus(db, roomID, PopupStatusAlreadySend)
		require.NoError(t, err)
		require.NotEmpty(t, records, "需要至少一条已发放的成就记录")

		// 找到指定成就配置 ID 的记录
		var targetRecord *FansRoomAchievement
		for _, record := range records {
			if record.AchievementConfigID == achievementConfigID {
				targetRecord = record
				break
			}
		}
		require.NotNil(t, targetRecord, "需要找到指定成就配置 ID 的记录")

		// 尝试更新已发放的记录，应该返回 0 行受影响（因为状态不匹配）
		rowAffected, err := targetRecord.UpdatePopupStatus(db, PopupStatusNotSend, PopupStatusAlreadySend)
		require.NoError(t, err)
		assert.EqualValues(t, 0, rowAffected, "更新已发放的记录应该返回 0 行受影响")
	})

	t.Run("更新不存在的成就记录", func(t *testing.T) {
		// 创建一个不存在的记录实例
		nonExistentRecord := &FansRoomAchievement{
			ID:                  99999, // 不存在的 ID
			RoomID:              roomID,
			AchievementConfigID: achievementConfigID,
			PopupStatus:         PopupStatusNotSend,
		}

		// 尝试更新不存在的记录，应该返回 0 行受影响
		rowAffected, err := nonExistentRecord.UpdatePopupStatus(db, PopupStatusNotSend, PopupStatusAlreadySend)
		require.NoError(t, err)
		assert.EqualValues(t, 0, rowAffected, "更新不存在的记录应该返回 0 行受影响")
	})
}

func TestMarkAsSent(t *testing.T) {
	roomID := int64(645321)
	achievementConfigID := int64(2) // 使用不同的配置 ID 避免冲突
	db := DB()

	t.Run("成功标记未发放的成就记录为已发放", func(t *testing.T) {
		// 创建一个未发放的测试记录
		testRecord := &FansRoomAchievement{
			RoomID:              roomID,
			AchievementConfigID: achievementConfigID,
			FansCount:           20,
			PopupStatus:         PopupStatusNotSend,
		}
		ok, err := testRecord.Create(db)
		require.NoError(t, err)
		require.True(t, ok, "测试记录创建失败")

		// 记录原始状态
		originalStatus := testRecord.PopupStatus
		assert.Equal(t, PopupStatusNotSend, originalStatus, "记录应该是未发放状态")

		// 调用 MarkAsSent 方法
		ok, err = testRecord.MarkAsSent(db)
		require.NoError(t, err)
		assert.True(t, ok, "应该成功标记为已发放")

		// 验证结构体实例的状态已更新
		assert.Equal(t, PopupStatusAlreadySend, testRecord.PopupStatus, "结构体实例的状态应该已更新")

		// 验证数据库中的记录状态已更新
		var updatedRecord FansRoomAchievement
		err = db.Table(fansRoomAchievementTableName).Where("id = ?", testRecord.ID).First(&updatedRecord).Error
		require.NoError(t, err)
		assert.Equal(t, PopupStatusAlreadySend, updatedRecord.PopupStatus, "数据库中的记录状态应该已更新")
	})

	t.Run("标记已发放的成就记录应该失败", func(t *testing.T) {
		// 创建一个已发放的测试记录
		testRecord := &FansRoomAchievement{
			RoomID:              roomID,
			AchievementConfigID: achievementConfigID + 1, // 使用不同的配置 ID
			FansCount:           30,
			PopupStatus:         PopupStatusAlreadySend,
		}
		ok, err := testRecord.Create(db)
		require.NoError(t, err)
		require.True(t, ok, "测试记录创建失败")

		// 记录原始状态
		originalStatus := testRecord.PopupStatus
		assert.Equal(t, PopupStatusAlreadySend, originalStatus, "记录应该是已发放状态")

		// 调用 MarkAsSent 方法
		ok, err = testRecord.MarkAsSent(db)
		require.NoError(t, err)
		assert.False(t, ok, "标记已发放的记录应该失败")

		// 验证结构体实例的状态未改变
		assert.Equal(t, PopupStatusAlreadySend, testRecord.PopupStatus, "结构体实例的状态应该未改变")
	})

	t.Run("标记不存在的成就记录应该失败", func(t *testing.T) {
		// 创建一个不存在的记录实例
		nonExistentRecord := &FansRoomAchievement{
			ID:                  99999, // 不存在的 ID
			RoomID:              roomID,
			AchievementConfigID: achievementConfigID,
			PopupStatus:         PopupStatusNotSend,
		}

		// 调用 MarkAsSent 方法
		ok, err := nonExistentRecord.MarkAsSent(db)
		require.NoError(t, err)
		assert.False(t, ok, "标记不存在的记录应该失败")

		// 验证结构体实例的状态未改变
		assert.Equal(t, PopupStatusNotSend, nonExistentRecord.PopupStatus, "结构体实例的状态应该未改变")
	})
}

func TestFindHighestByRoomID(t *testing.T) {
	db := DB()

	t.Run("找到最高等级的成就记录", func(t *testing.T) {
		roomID := int64(645322) // 使用不同的房间 ID 避免冲突

		// 创建多个不同粉丝数量的测试记录
		testRecords := []*FansRoomAchievement{
			{
				RoomID:              roomID,
				AchievementConfigID: 10,
				FansCount:           100, // 最高
				PopupStatus:         PopupStatusAlreadySend,
			},
			{
				RoomID:              roomID,
				AchievementConfigID: 11,
				FansCount:           50,
				PopupStatus:         PopupStatusNotSend,
			},
			{
				RoomID:              roomID,
				AchievementConfigID: 12,
				FansCount:           20,
				PopupStatus:         PopupStatusAlreadySend,
			},
		}

		// 创建测试记录
		for _, record := range testRecords {
			ok, err := record.Create(db)
			require.NoError(t, err)
			require.True(t, ok, "测试记录创建失败")
		}

		// 查找最高等级的成就记录
		highest, err := FindHighestByRoomID(db, roomID)
		require.NoError(t, err)
		require.NotNil(t, highest, "应该找到最高等级的记录")

		// 验证返回的是粉丝数量最高的记录
		assert.Equal(t, roomID, highest.RoomID)
		assert.Equal(t, int64(10), highest.AchievementConfigID)
		assert.Equal(t, 100, highest.FansCount)
		assert.Equal(t, PopupStatusAlreadySend, highest.PopupStatus)
	})

	t.Run("房间没有成就记录", func(t *testing.T) {
		nonExistentRoomID := int64(999999) // 不存在的房间 ID

		// 查找不存在房间的成就记录
		highest, err := FindHighestByRoomID(db, nonExistentRoomID)
		require.NoError(t, err)
		assert.Nil(t, highest, "不存在的房间应该返回 nil")
	})

	t.Run("房间只有一个成就记录", func(t *testing.T) {
		roomID := int64(645323) // 使用不同的房间 ID

		// 创建一个测试记录
		testRecord := &FansRoomAchievement{
			RoomID:              roomID,
			AchievementConfigID: 13,
			FansCount:           75,
			PopupStatus:         PopupStatusNotSend,
		}
		ok, err := testRecord.Create(db)
		require.NoError(t, err)
		require.True(t, ok, "测试记录创建失败")

		// 查找成就记录
		highest, err := FindHighestByRoomID(db, roomID)
		require.NoError(t, err)
		require.NotNil(t, highest, "应该找到唯一的记录")

		// 验证返回的记录
		assert.Equal(t, testRecord.ID, highest.ID)
		assert.Equal(t, roomID, highest.RoomID)
		assert.Equal(t, int64(13), highest.AchievementConfigID)
		assert.Equal(t, 75, highest.FansCount)
		assert.Equal(t, PopupStatusNotSend, highest.PopupStatus)
	})

	t.Run("多个记录相同粉丝数量", func(t *testing.T) {
		roomID := int64(645324) // 使用不同的房间 ID

		// 创建多个相同粉丝数量的测试记录
		testRecords := []*FansRoomAchievement{
			{
				RoomID:              roomID,
				AchievementConfigID: 14,
				FansCount:           200, // 相同的粉丝数量
				PopupStatus:         PopupStatusAlreadySend,
			},
			{
				RoomID:              roomID,
				AchievementConfigID: 15,
				FansCount:           200, // 相同的粉丝数量
				PopupStatus:         PopupStatusNotSend,
			},
		}

		// 创建测试记录
		for _, record := range testRecords {
			ok, err := record.Create(db)
			require.NoError(t, err)
			require.True(t, ok, "测试记录创建失败")
		}

		// 查找最高等级的成就记录
		highest, err := FindHighestByRoomID(db, roomID)
		require.NoError(t, err)
		require.NotNil(t, highest, "应该找到一个记录")

		// 验证返回的记录（应该是其中一个，具体哪个取决于数据库排序）
		assert.Equal(t, roomID, highest.RoomID)
		assert.Equal(t, 200, highest.FansCount)
		assert.Contains(t, []int64{14, 15}, highest.AchievementConfigID, "应该是其中一个配置 ID")
	})
}
