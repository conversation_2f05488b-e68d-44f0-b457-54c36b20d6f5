package livefansboxtask

import (
	"encoding/json"
	"time"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/liveim"
	mongodbgift "github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansbox"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxusertask"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 宝箱任务状态
const (
	StatusUnfinished = 0 // 未完成
	StatusFinished   = 1 // 已完成
)

// JoinEnergy 新加入粉丝团可贡献的能量值
const JoinEnergy = 50

// LiveFansBoxTask 粉丝团宝箱任务表
type LiveFansBoxTask struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 更新时间，单位：秒

	Bizdate   string `gorm:"column:bizdate"`    // 业务日期（格式：2025-06-16）
	RoomID    int64  `gorm:"column:room_id"`    // 直播间 ID
	FansCount int64  `gorm:"column:fans_count"` // 当前宝箱任务的粉丝数量（当天零点前加入粉丝团的有效粉丝数量）
	Level     int    `gorm:"column:level"`      // 宝箱等级（同 live_fans_box 表 level）
	Energy    int    `gorm:"column:energy"`     // 当日的能量值
	Status    int    `gorm:"column:status"`     // 状态；0：未完成；1：已完成
}

const tableName = "live_fans_box_task"

// TableName for current model
func (LiveFansBoxTask) TableName() string {
	return tableName
}

// DB .
func (l LiveFansBoxTask) DB() *gorm.DB {
	return service.LiveDB.Table(l.TableName())
}

// BeforeCreate automatically sets columns create_time and modified_time
func (l *LiveFansBoxTask) BeforeCreate() (err error) {
	now := goutil.TimeNow().Unix()
	l.CreateTime = now
	l.ModifiedTime = now

	return nil
}

// BeforeUpdate automatically sets column modified_time
func (l *LiveFansBoxTask) BeforeUpdate() (err error) {
	l.ModifiedTime = goutil.TimeNow().Unix()
	return nil
}

// TaskInfo 任务信息
type TaskInfo struct {
	ID            int64  `gorm:"column:id;primary_key"` // 主键
	RoomID        int64  `gorm:"column:room_id"`        // 直播间 ID
	Level         int    `gorm:"column:level"`          // 宝箱等级（同 live_fans_box 表 level）
	Status        int    `gorm:"column:status"`         // 状态；0：未完成；1：已完成
	CurrentEnergy int    `gorm:"column:current_energy"` // 当前能量值
	TargetEnergy  int    `gorm:"column:target_energy"`  // 目标能量值（解锁宝箱所需能量值）
	More          []byte `gorm:"column:more"`           // 更多信息（宝箱奖品、奖品权重、每日库存等）

	MoreInfo livefansbox.More `gorm:"-"`
}

// GetHasStockRewards 获取宝箱任务中还可抽奖领取（还有剩余库存）的奖励
func (t *TaskInfo) GetHasStockRewards() ([]livefansbox.RewardInfo, error) {
	rewardsLen := len(t.MoreInfo.Rewards)
	stockRewards := make([]livefansbox.RewardInfo, 0, rewardsLen)
	if rewardsLen == 0 {
		return stockRewards, nil
	}
	rewardConsumedStocks, err := livefansbox.GetConsumedStock(t.RoomID, t.Level, t.MoreInfo.Rewards)
	if err != nil {
		return stockRewards, err
	}
	for i, consumedStock := range rewardConsumedStocks {
		reward := t.MoreInfo.Rewards[i]
		if consumedStock >= reward.DailyStock {
			continue
		}
		stockRewards = append(stockRewards, reward)
	}
	return stockRewards, nil
}

// FindTodayTask 获取直播间今日宝箱任务信息
func FindTodayTask(roomID int64) (*TaskInfo, error) {
	todayFormat := goutil.TimeNow().Format(goutil.TimeFormatYMD)
	task := &TaskInfo{}
	err := LiveFansBoxTask{}.DB().Table(tableName+" AS t1").
		Select("t1.id, t1.room_id, t1.level, t1.status, t1.energy AS current_energy, t2.energy AS target_energy, t2.more").
		Joins("LEFT JOIN "+livefansbox.LiveFansBox{}.TableName()+" AS t2 ON t1.level = t2.level").
		Where("t1.room_id = ? AND t1.bizdate = ?", roomID, todayFormat).
		Take(task).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	if len(task.More) > 0 {
		err := json.Unmarshal(task.More, &task.MoreInfo)
		if err != nil {
			return nil, err
		}
	}
	return task, nil
}

func getDB(tx *gorm.DB) *gorm.DB {
	if tx != nil {
		return tx.Table(tableName)
	}
	return service.LiveDB.Table(tableName)
}

// FindOrCreate 查询或新建粉丝团宝箱任务信息
func FindOrCreate(roomID int64, date time.Time) (*LiveFansBoxTask, error) {
	return findOrCreate(nil, roomID, date)
}

func findOrCreate(tx *gorm.DB, roomID int64, date time.Time) (*LiveFansBoxTask, error) {
	var task LiveFansBoxTask
	dateFormat := date.Format(goutil.TimeFormatYMD)
	err := getDB(tx).Table(tableName).Where("room_id = ? AND bizdate = ?",
		roomID, dateFormat).Take(&task).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return nil, err
	}
	if task.ID > 0 {
		return &task, nil
	}

	boxs, err := livefansbox.ListFansBox()
	if err != nil {
		return nil, err
	}
	filter := bson.M{
		"room_id":      roomID,
		"status":       bson.M{"$gt": livemedal.StatusPending},
		"created_time": bson.M{"$lt": util.BeginningOfDay(date)},
	}
	count, err := livemedal.CountMedal(filter)
	if err != nil {
		return nil, err
	}
	var box *livefansbox.LiveFansBox
	for i := len(boxs) - 1; i >= 0; i-- {
		if count >= boxs[i].FansCount {
			box = boxs[i]
			break
		}
	}
	if box == nil {
		logger.Errorf("未匹配到粉丝数对应的粉丝团宝箱信息，fans_count: %d", count)
		return nil, nil
	}

	newTask := &LiveFansBoxTask{
		Bizdate:   dateFormat,
		RoomID:    roomID,
		FansCount: count,
		Level:     box.Level,
		Energy:    0,
		Status:    StatusUnfinished,
	}
	if err = newTask.DB().Create(newTask).Error; err == nil {
		return newTask, nil
	}
	if !servicedb.IsUniqueError(err) {
		return nil, err
	}

	var existTask *LiveFansBoxTask
	// 触发唯一索引时，防止主从数据库同步延迟问题，使用事务从主库查询数据
	err = servicedb.Tx(service.LiveDB.Table(tableName), func(tx *gorm.DB) error {
		existTask, err = findOrCreate(tx, roomID, date)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return existTask, nil
}

// ContributeEnergy 为粉丝团宝箱贡献能量
func (t *TaskInfo) ContributeEnergy(roomID, userID int64, energyAdd int) (updated bool, refreshed bool, err error) {
	if !livefansbox.EnableFansBox() {
		return false, false, nil
	}
	if t.Status != StatusUnfinished {
		return false, false, nil
	}

	updated = false
	refreshed = false
	err = servicedb.Tx(LiveFansBoxTask{}.DB(), func(tx *gorm.DB) error {
		ok, err := t.updateTaskEnergyInTx(tx, energyAdd)
		if err != nil {
			return err
		}
		if !ok {
			return nil
		}

		err = livefansboxusertask.UpsertUserEnergyInTx(tx, roomID, userID, t.ID, energyAdd)
		if err != nil {
			return err
		}

		updated = true

		err = t.refreshFromDBInTx(tx)
		if err != nil {
			// refresh 失败不应该导致事务回滚，只记录日志
			logger.Errorf("刷新粉丝团宝箱任务数据失败: %v", err)
			return nil
		}

		refreshed = true
		return nil
	})

	return updated, refreshed, err
}

func (t *TaskInfo) updateTaskEnergyInTx(tx *gorm.DB, energyAdd int) (bool, error) {
	result := tx.Table(tableName).Where("id = ? AND status = ?", t.ID, StatusUnfinished).
		Updates(map[string]interface{}{
			"energy": gorm.Expr("LEAST(energy + ?, ?)", energyAdd, t.TargetEnergy),
			"status": gorm.Expr("IF(energy + ? >= ?, ?, status)",
				energyAdd, t.TargetEnergy, StatusFinished),
		})
	if result.Error != nil {
		return false, result.Error
	}
	if result.RowsAffected == 0 {
		return false, nil
	}
	return true, nil
}

func (t *TaskInfo) refreshFromDBInTx(tx *gorm.DB) error {
	var updatedTask TaskInfo
	err := tx.Table(tableName+" AS t1").
		Select("t1.id, t1.room_id, t1.level, t1.status, t1.energy AS current_energy, t2.energy AS target_energy, t2.more").
		Joins("LEFT JOIN "+livefansbox.LiveFansBox{}.TableName()+" AS t2 ON t1.level = t2.level").
		Where("t1.id = ?", t.ID).
		Take(&updatedTask).Error
	if err != nil {
		return err
	}

	// 更新当前对象的字段
	t.RoomID = updatedTask.RoomID
	t.Level = updatedTask.Level
	t.Status = updatedTask.Status
	t.CurrentEnergy = updatedTask.CurrentEnergy
	t.TargetEnergy = updatedTask.TargetEnergy

	// 更新 More 信息
	if len(updatedTask.More) > 0 {
		err := json.Unmarshal(updatedTask.More, &t.MoreInfo)
		if err != nil {
			return err
		}
	}

	return nil
}

// GiftContribution 送礼贡献能量参数
type GiftContribution struct {
	RoomID  int64             // 直播间 ID
	UserID  int64             // 用户 ID
	Gift    *mongodbgift.Gift // 礼物信息
	GiftNum int               // 礼物数量
}

// ContributeFromGift 送礼贡献粉丝团宝箱能量
func (t *TaskInfo) ContributeFromGift(param GiftContribution) (updated bool, refreshed bool, err error) {
	if !livefansbox.EnableFansBox() {
		return false, false, nil
	}
	if !isEligibleGiftType(param.Gift) {
		return false, false, nil
	}
	isFansMember, err := livemedal.HasUserOwnedMedal(param.UserID, param.RoomID)
	if err != nil {
		return false, false, err
	}
	if !isFansMember {
		return false, false, nil
	}
	energy := param.Gift.Price * int64(param.GiftNum)
	if energy <= 0 {
		return false, false, nil
	}
	return t.ContributeEnergy(param.RoomID, param.UserID, int(energy))
}

func isEligibleGiftType(g *mongodbgift.Gift) bool {
	if g == nil {
		return false
	}
	if g.Type == mongodbgift.TypeFree {
		return false
	}
	return true
}

// TaskUpdateMessage 粉丝团宝箱任务更新消息
type TaskUpdateMessage struct {
	Type    string       `json:"type"`
	Event   string       `json:"event"`
	RoomID  int64        `json:"room_id"`
	FansBox *fansBoxInfo `json:"fans_box"`
}

// fansBoxInfo 粉丝团宝箱信息
type fansBoxInfo struct {
	BoxTask *boxTaskInfo `json:"box_task"`
}

// boxTaskInfo 宝箱任务信息
type boxTaskInfo struct {
	ID            int64 `json:"id"`             // 任务 ID
	TargetEnergy  int   `json:"target_energy"`  // 解锁宝箱所需能量值
	CurrentEnergy int   `json:"current_energy"` // 已达成的能量值
	Status        int   `json:"status"`         // 宝箱完成状态，0：未完成；1：已完成
}

// NewTaskUpdateMessage 创建粉丝团宝箱任务更新消息
func (t *TaskInfo) NewTaskUpdateMessage() *TaskUpdateMessage {
	return &TaskUpdateMessage{
		Type:   liveim.TypeFansBox,
		Event:  liveim.EventFansBoxTaskUpdate,
		RoomID: t.RoomID,
		FansBox: &fansBoxInfo{
			BoxTask: &boxTaskInfo{
				ID:            t.ID,
				TargetEnergy:  t.TargetEnergy,
				CurrentEnergy: t.CurrentEnergy,
				Status:        t.Status,
			},
		},
	}
}

// Send 发送粉丝团宝箱任务更新广播消息
func (m *TaskUpdateMessage) Send() error {
	return userapi.Broadcast(m.RoomID, m)
}

// SendTaskUpdate 发送粉丝团宝箱任务更新消息
func (t *TaskInfo) SendTaskUpdate() error {
	message := t.NewTaskUpdateMessage()
	return message.Send()
}

// ListByRoomIDs 获取指定直播间的宝箱任务列表
func ListByRoomIDs(roomIDs []int64, date time.Time) ([]*LiveFansBoxTask, error) {
	var tasks []*LiveFansBoxTask
	err := LiveFansBoxTask{}.DB().Where("room_id IN (?) AND bizdate = ?", roomIDs, date.Format(goutil.TimeFormatYMD)).Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}
