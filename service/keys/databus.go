package keys

import (
	"github.com/MiaoSiLa/missevan-go/service/cache"
)

// NOTICE: databus key 不可以使用冒号（RocketMQ key 含有冒号会报错），使用正斜杠符号分割

// KeyGashaponBuff0 扭蛋 buff
const KeyGashaponBuff0 cache.KeyFormat = "gashapon:buff" // STRING

// KeyAnnualQualifyQuest1 直播间的年度直播盛典资格赛任务
// params: 主播 ID
const KeyAnnualQualifyQuest1 cache.KeyFormat = "annual:qualify_quest:%d" // STRING

// KeyLiveSpend1 用户直播消费
// params: 用户 ID
// TODO: 考虑兼容的情况下重命名成 live:spend:%d
var KeyLiveSpend1 = cache.DatabusFormatter{
	"highness:spend:%d", // STRING
	"highness:spend:",
}

// KeyLiveMedalChange1 用户直播粉丝勋章的增减
// params: 用户 ID
var KeyLiveMedalChange1 = cache.DatabusFormatter{
	"live/medal/change/%d",
	"live/medal/change/",
}

// DelayKeyPKMatchingStart1 PK 匹配超时
// params: 房间号
const DelayKeyPKMatchingStart1 cache.KeyFormat = "pk/matching/start/%d" // STRING

// DelayKeyPKMatchingTimeout1 PK 匹配超时
// params: 房间号
const DelayKeyPKMatchingTimeout1 cache.KeyFormat = "pk/matching/timeout/%d" // STRING

// DelayKeyPKFightingFinish1 PK 结束进入惩罚期消息
// params: PK ID
const DelayKeyPKFightingFinish1 cache.KeyFormat = "pk/fighting/finish/%s" // STRING

// DelayKeyPKPunishFinish1 PK 惩罚期结束消息
// params: PK ID
const DelayKeyPKPunishFinish1 cache.KeyFormat = "pk/punish/finish/%s" // STRING

// KeyActivityEvent196Quest1 活动 196 积分任务
// params: 主播 ID
const KeyActivityEvent196Quest1 cache.KeyFormat = "activity:event196_quest:%d"

// DelayDatabusKeyActivityRPC1 活动延时队列 key
// params: 活动 rpc 传入的 key
var DelayDatabusKeyActivityRPC1 = cache.DatabusFormatter{
	"activity/rpc/%s", // STRING
	"activity/rpc/",
}

// 礼物红包相关
var (
	// DelayKeyRedPacketExpired1 礼物红包过期
	// params: live_red_packet id
	DelayKeyRedPacketExpired1 = cache.DatabusFormatter{
		"redpacket/expired/%s", // STRING
		"redpacket/expired/",
	}
	// DelayKeyRedPacketSetGrabbing1 礼物红包修改为可抢红包
	// params: live_red_packet id
	DelayKeyRedPacketSetGrabbing1 = cache.DatabusFormatter{
		"redpacket/set/grabbing/%s", // STRING
		"redpacket/set/grabbing/",
	}
)

// 福袋相关
var (
	// DelayKeyDrawLuckyBag1 福袋开奖
	// params: lucky_bag id
	DelayKeyDrawLuckyBag1 = cache.DatabusFormatter{
		"luckybag/draw/%d",
		"luckybag/draw/",
	}
)

// KeyDrawLiveLuckyBox1 宝箱任务发奖
var KeyDrawLiveLuckyBox1 = cache.DatabusFormatter{
	"live/luckybox/draw/%d",
	"live/luckybox/draw/",
}

var (
	// DelayKeyMultiConnectMatchTimeout1 主播连线 匹配超时
	// params: 连线匹配 ID
	DelayKeyMultiConnectMatchTimeout1 = cache.DatabusFormatter{
		"multiconnect/match/timeout/%d",
		"multiconnect/match/timeout/",
	}
)

// KeyRecomendExposureLog1 直播推荐服务端曝光日志
// params: user_id 或 :crc32(ip)
var KeyRecomendExposureLog1 = cache.DatabusFormatter{
	"recommend_exposure_log:%s",
	"recommend_exposure_log",
}

// 粉丝成就相关
var (
	// DelayKeyFansAchievementPopup1 粉丝成就弹窗延时消息
	// params: room_id
	DelayKeyFansAchievementPopup1 = cache.DatabusFormatter{
		"fans/achievement/popup/%d",
		"fans/achievement/popup/",
	}
)
